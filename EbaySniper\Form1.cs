﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Media;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraBars.Docking2010;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraBars.Ribbon.Gallery;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraLayout;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using ExceptionReporting;
using NLog;
using uBuyFirst.AI;
using uBuyFirst.Auth;
using uBuyFirst.CefBrowser;
using uBuyFirst.CustomClasses;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.GUI;
using uBuyFirst.Images;
using uBuyFirst.Item;
using uBuyFirst.License;
using uBuyFirst.MQTT;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing;
using uBuyFirst.Purchasing.Cookies;

using uBuyFirst.Restocker.Services;
using uBuyFirst.Search;
using uBuyFirst.Search.Status;
using uBuyFirst.Security;
using uBuyFirst.Services;
using uBuyFirst.SkuManager;
using uBuyFirst.Services.Caching;
using uBuyFirst.Stats;
using uBuyFirst.SubSearch;
using uBuyFirst.Telegram;
using uBuyFirst.Time;
using uBuyFirst.Tools;
using uBuyFirst.Update;
using uBuyFirst.Views;
using uBuyFirst.Watchlist;
using SiteCodeType = eBay.Service.Core.Soap.SiteCodeType;
using DevExpress.XtraRichEdit.Model;
using IniParser;

[assembly:
    Obfuscation(Exclude = false,
        Feature = "preset(minimum);"
                  + "+anti debug;"
                  + "+anti dump;"
                  + "+anti ildasm;"
                  + "+anti tamper(key=dynamic);"
                  + "+constants;"
                  + "+ctrl flow;"
                  + "+invalid metadata;"
                  + "+ref proxy;"
                  + "-rename;")]
[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]

namespace uBuyFirst
{
    public partial class Form1
    {
        //Modules - Alert, Tracking, Logging, eBay Searches, eBay Accounts, PlaceOffer, Filtering, Pushbullet, Specifics, Browser, Licensing, Settings, Updater

        //tracking

        //lists
        public static BindingList<EbayAccount> EBayAccountsList;
        private WatchlistManager? _watchlistManager;
        internal QueryList _ebaySearches;

        /// <summary>
        /// Public access to the eBay searches collection for use by filter actions
        /// </summary>
        public QueryList EbaySearches => _ebaySearches;

        //Utils
        private Updater _updater;
        private readonly GuiChanger _guiChanger;
        private FocusRouter _focusRouter;
        private PushbulletSender? _pushbullet;
        private TelegramSender? _telegramSender;
        private SoundPlayer _myPlayer;
        private readonly AblyClient _ablyClient;
        private readonly MQTTManager _mqttManager;
        public static readonly Logger Log = LogManager.GetLogger("Logger");
        public static LicenseUtility LicenseUtility;
        public static SecurePhpConnection Secure = new();
        public FilterAdapter FilterAdapter1;

        public SearchService _searchService;



        //time
        private System.Windows.Forms.Timer _1SecTimer;
        private System.Windows.Forms.Timer _1HourTimer;
        private System.Windows.Forms.Timer _searchTermSyncTimer;
        private int _cooldownSecondsRemaining1;
        private string _originalRefreshButtonText;


        //Forms
        public static Form1 Instance { get; private set; }

        private FormReportDialog _formReportDialog;


        public static SynchronizationContext _synchronizationContext;
        private Point _filterItemLocationPoint = Point.Empty;

        public static bool _showHighBidder;
        private static GetItemsStatus _checkItemsStatus;

        private TrayManager _trayManager;
        private ViewReporter _viewReporter;
        private PicManager _picManager;

        public static bool EBayAccountInitCompleted { get; set; }
        private static bool _saveActiveWorkspace = true;
        public static BuyingService BuyingService;
        public static Analytics? GoogleAnalytics;
        private readonly CefBrowserManager _cefBrowserManager;

        private object _chkLargeImagesOnHoverValueChangedLock = new object();
        public static int LastSliderValueSmall = 100;
        public static int LastSliderValueLarge = 100;

        //Services
        private readonly ICountryService _countryService;
        private readonly ISellerService _sellerService;
        private readonly ICacheService<string, string> _sellerCacheService;

        private void InitVars()
        {
            BaseGallery.AllowHoverAnimation = false;
            _synchronizationContext = SynchronizationContext.Current;
            ExM.ExceptionsList = new List<string>();
            EBayAccountsList = new BindingList<EbayAccount>();
            EBayAccountsList.ListChanged += EbayAccount.EBayAccountsList_ListChanged;
            ProgramState.Isdebug = Debugger.IsAttached;
            ProgramState.Idlesw = new Stopwatch();
            ProgramState.LastSelectedItemID = "";
            ProgramState.TotalRunningStopwatch = new Stopwatch();
            ProgramState.SearchingStopwatchGA4 = new Stopwatch();
            ExM.Reporter = new ExceptionReporter();
            ExM.SynchronizationContextCurrent = SynchronizationContext.Current;
            Stat.TotalItemsProcessed = 0;
            _myPlayer = new SoundPlayer { Stream = Resources.binbloop };

            ProgramState.PublicIp = "************";

            _focusRouter = new FocusRouter(webBrowser1);

            ResultsView.DockManager = dockManager1;
            ResultsView.RepositoryItemViews = repositoryItemViews;
            FilterAdapter1 = new FilterAdapter();
            _trayManager = new TrayManager(this);

            TopRowFocus.FocusTimer.SynchronizingObject = Instance;
            TopRowFocus.FocusTimer.AutoReset = false;
            TopRowFocus.FocusTimer.Elapsed += FocusTimer_Elapsed;
            TopRowFocus.FocusTimer.SynchronizingObject = this;

            _picManager = new PicManager(galleryControl1);

            //CategoryService = new CategoryService(ap);
        }
        public Form1(ICountryService countryService, ISellerService sellerService, ICacheService<string, string> cacheService)
        {
            _countryService = countryService ?? throw new ArgumentNullException(nameof(countryService));
            _sellerService = sellerService ?? throw new ArgumentNullException(nameof(sellerService));
            _sellerCacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));

            InitializeComponent();
            try
            {
                ProgramState.UBFVersion = ProductVersion;
                Instance = this;
                webBrowser1.PreviewKeyDown += FocusRouter.WebBrowserKeyDown;

                // Subscribe to filter removal logging event
                XFilterManager.FilterRemovalLogged += OnFilterRemovalLogged;

                MakeRunButtonStart();
                dockManager1.BeginUpdate();
                InitVars();

                // Initialize validation warning manager
                _validationWarnings = new TreeListValidationWarningManager();

                // Setup TreeList tooltips for validation warnings
                SetupTreeListTooltips();
                NetTools.InitHttPprotocol();

                //defer
                Loggers.SetupCloudLogging();
                Upgrader.TryUpgrade();

                ProgramState.HWID = LicenseUtility.GetHWID();
                _ablyClient = new AblyClient(_ => { });
                _mqttManager = new MQTTManager(PostMQQTItemToResultsGrid);
                Log.Info("Started version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ";" + Environment.OSVersion);

                //if (!ProgramState.Isdebug)
                {
                    Program.DeregisterExceptionHandlers();
                    AppDomain.CurrentDomain.UnhandledException += ExM.ubuyExceptionHandler;
                    Application.ThreadException += ExM.ubuyExceptionHandler;
                    TaskScheduler.UnobservedTaskException += ExM.ubuyExceptionTaskHandler;
                }

                Folders.SetupFolders();

                ItemSpecifics.CategorySpecificsList = new CustomBindingList<CategorySpecific>();
                ItemSpecifics.CategorySpecificsList.ListChanged += ItemSpecifics.CategorySpecificsList_ListChanged;
                ItemSpecifics.CategorySpecificsList.ItemDeleting += ItemSpecifics.CategorySpecificsList_ItemDeleting;
                var jsonFields = new string[]{
                    "Part Number Mismatch",
                    "Door is missing",
                    "Terminal block is missing",
                    "Repair evaluation only",
                    "For parts or not working",
                    "Part# text",
                    "Part# box pic",
                    "Part# unit pic",
                    "SN# text",
                    "SN# box pic",
                    "SN# unit pic",
                    "Reasoning"}
                ;
                AiAnalysis.AiColumnsList = new CustomBindingList<string>
                {
                };
                foreach (var field in jsonFields)
                {
                    AiAnalysis.AiColumnsList.Add(AiAnalysis.AiPrefix + field);
                }


                AiAnalysis.AiColumnsList.ListChanged += AiAnalysis.AiColumnsList_ListChanged;
                AiAnalysis.AiColumnsList.ItemDeleting += AiAnalysis.AiColumnsList_ItemDeleting;
                SetupGUI();
                WatchlistManager.WatchlistGridControl = GridBuilder.CreateGridControl("Watchlist");
                lciWatchlist.Control = WatchlistManager.WatchlistGridControl;

                // Initialize WatchlistManager with default 5-minute interval
                _watchlistManager = new WatchlistManager(SynchronizationContext.Current, HandleNewItem);
                _watchlistManager.ItemBecameAvailable += WatchlistManager_ItemBecameAvailable; // Subscribe AFTER instantiation

                // Subscribe to other WatchlistManager events
                _watchlistManager.RefreshStarted += WatchlistManager_RefreshStarted;
                _watchlistManager.RefreshCompleted += WatchlistManager_RefreshCompleted;

                // Store original button text and initialize cooldown timer
                _originalRefreshButtonText = btnRefreshWatchlist.Text;
                var configPath = Path.Combine(Folders.Settings, "config.cfg");
                LoadSettings(configPath);
                WebView.DisableClickSounds();

                Helpers.CheckDotNetVersion();

                _cefBrowserManager = new CefBrowserManager();
                var filesMissing = _cefBrowserManager.AreRequiredFilesMissing();
                if (!filesMissing)
                {
                    _cefBrowserManager.InitializeCefEngine();
                }

                _guiChanger = new GuiChanger(Instance, _cefBrowserManager, panelCefBrowser);
                GridViewEvents.GuiChanger = _guiChanger;
                repositoryItemComboBoxSite.Items.AddRange(Intl.CountryProvider.GetEbaySiteNameList());
                repositoryItemComboBoxLocatedIn.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeListWithAny());
                repositoryItemComboBoxShipsTo.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeList());
                BuyingService = new BuyingService();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
                Application.Exit();
            }

            //var geminiService = new GeminiService();
            //geminiService.SendHelloMessage();
        }



        public Form1(bool bIsFromTrialPrompt)
        {
            InitializeComponent();
            try
            {
                if (bIsFromTrialPrompt)
                {
                    Folders.SetupFolders();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
                Application.Exit();
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            Debug.WriteLine(DateTime.Now.ToString("T") + "**Loading");
            dockManager1.LayoutVersion = "d3";
            dockManager1.ForceInitialize();
            documentManager1.ForceInitialize();

            // Add search icon to the folder image collection
            svgImageCollectionFolders.Add("search", Properties.Resources.search);

            repositoryItemViews.SelectedIndexChanged += RepositoryItemViews_SelectedIndexChanged;
            repositoryItemViews.ButtonClick += RepositoryItemViews_ButtonClick;

            // Subscribe to bulk changes event for TreeList refresh
            Search.KeywordFolder.BulkChangesApplied += OnBulkChangesApplied;

            // Wire up the checkbox event handler for folder cascading
            // REMOVED: repositoryItemCheckEditEnabled.EditValueChanged += repositoryItemCheckEditEnabled_EditValueChanged;
            // This event is already wired up in Designer.cs - duplicate subscription caused infinite loop!

            UserLookAndFeel.Default.ActiveLookAndFeel.StyleChanged += ActiveLookAndFeel_StyleChanged;
            ChangePanelHeader();
            SetTreeListCheckboxesState();
        }

        private void Form1_Shown(object sender, EventArgs e)
        {
            Debug.WriteLine(DateTime.Now.ToString("T") + "**Showing");

            ShowAlertNotification("Windows 7 Support Notice", "uBuyFirst will soon stop supporting Windows 7.\nPlease upgrade to a newer version of Windows to ensure continued functionality.");
            LoadWorkspacesOnFormLoad();
            LoadAndApplyWorkSpace();
            var obsoletePanel = dockManager1.Panels.ToList().FirstOrDefault(p => p.ID == new Guid("92581858-ecdc-4d88-be24-9bb7948442dc"));
            obsoletePanel?.Dispose();
            ResultsView.TogglePanelsVisibility();
            dockManager1.EndUpdate();

            _1SecTimer = new System.Windows.Forms.Timer();
            _1SecTimer.Tick += _1secTimer_Elapsed;
            _1SecTimer.Interval = (int)TimeSpan.FromSeconds(1).TotalMilliseconds;
            _1SecTimer.Tag = 0;
            _1SecTimer.Start();

            #region Licensing

            LicenseUtility = new LicenseUtility();
            LicenseUtility.SetDefaultLimits();

            InitUpdater();
            ImageCleaner.StartCleanUpTimer();
            uBuyFirst.Restocker.Services.RestockHtmlCleaner.StartCleanupTimer();
            //after license completed
            var fromCurrentSynchronizationContext = TaskScheduler.FromCurrentSynchronizationContext();
            LicenseUtility
                .CheckLicense()
                .ContinueWith(_ =>
                    ConnectionConfig.SetApiConfig()
                    .ContinueWith(_ =>
                    {
                        barStaticLicense.Caption = LicenseUtility.GetLicenseStatusText();
                        Loggers.InitGoogleAnalyticsV4(ProgramState.SerialNumberShort, LicenseUtility.CurrentSubscriptionType);

                        ServicePointManager.ServerCertificateValidationCallback = Helpers.BuyItNowConfirmation;
                        if (!ConnectionConfig.TradingAPIEnabled)
                        {
                            barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
                        }

                        ToggleCheckoutPermission();
                        ToggleRestockPermission();
                        CookieManager.ReadChromeVersion();
                        var profileListLoaded = AssignProfileCombobox();

                        // Cookie initialization removed - cookies will be fetched from Firefox only when needed
                        // and when cache is empty or cleared due to logout detection

                        if (ConnectionConfig.CheckoutEnabled && profileListLoaded)
                        {
                            barCheckImmediatePayment.Checked = CreditCardService.CreditCardPaymentEnabled;
                        }
                        else
                        {
                            barCheckImmediatePayment.Checked = false;
                            CreditCardService.CreditCardPaymentEnabled = false;
                        }

                        if (ConnectionConfig.SkipBuyConfirmation)
                        {
                            barCheckNoConfirmations.Checked = UserSettings.SkipBuyConfirmation;
                        }
                        else
                        {
                            barCheckNoConfirmations.Checked = false;
                            UserSettings.SkipBuyConfirmation = false;
                        }

                        if (ConnectionConfig.RestockerEnabled)
                        {
                            barCheckItemRestock.Checked = UserSettings.RestockerEnabled;
                        }
                        else
                        {
                            barCheckItemRestock.Checked = false;
                            UserSettings.RestockerEnabled = false;
                        }

                        if (!ConnectionConfig.RestockerEnabled)
                        {
                            UserSettings.RestockerEnabled = false;
                        }
                        barCheckItemRestock.Checked = UserSettings.RestockerEnabled;


                        ribbonPageData.Visible = ConnectionConfig.ShowDataButton;
                        ribbonPageGroupExternalData.Visible = ConnectionConfig.ShowDataButton;
                        barButtonItemGetExternalData.Visibility = ConnectionConfig.ShowDataButton ? BarItemVisibility.Always : BarItemVisibility.Never;
                        barButtonItemGetExternalData.Enabled = ConnectionConfig.ShowDataButton;

                        timeSpanWatchlistRefreshInterval.Properties.MinValue = TimeSpan.FromSeconds(ConnectionConfig.WatchlistUpdateInterval);

                        // Initialize AutoPurchase system visibility based on ConnectionConfig
                        InitializeAutoPurchaseSystemVisibility();

                        LicenseUtility.LicenseCheckCompleted = true;
                        InitEbayAccounts();
                    }, fromCurrentSynchronizationContext));

            #endregion Licensing

            InitBGtasks();
            _viewReporter = new ViewReporter(this);

            Log.Info("Loaded version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ProgramState.SerialNumber);

            barCheckItemSoundAlert.CheckedChanged += barCheckItemSoundAlert_CheckedChanged;
            galleryControl1.MouseLeave += _focusRouter.FocusBrowser;
            lstchkXfilterList.ItemCheck += lstchkXfilterList_ItemCheck;

            barStaticErrorsVal.Visibility = BarItemVisibility.Always;
            if (Program.Sandbox)
            {
                Text = "Sandbox mode";
                linkeBayPrivacyPolicy.Visible = true;
                linkeBayUserAgreement.Visible = true;
                linkReportItem.Visible = true;
                picBoxEbayLogo.Visible = true;
            }

            if (Config.SafeEnabled)
            {
                linkeBayPrivacyPolicy.Visible = true;
                linkeBayUserAgreement.Visible = true;
                linkReportItem.Visible = true;
                picBoxEbayLogo.Visible = true;
            }

            _searchService = new SearchService(SearchConfigManager.Instance.GetConfigForSerialization(), LicenseUtility, _viewReporter, _synchronizationContext, this.HandleNewItem);

            if (barCheckItemAutoStartSearch.Checked || Upgrader.ResumeSearchAfterUpgrade)
            {
                barButtonStart.PerformClick();
            }

            _synchronizationContext.Send(state => SaveSettings(), null);

            UpdateTodaySpentDisplay();

            var newHeight = Convert.ToInt32(16 * DpiProvider.Default.DpiScaleFactor + 2);
            if (treeList1.RowHeight > 0 && treeList1.RowHeight < newHeight)
            {
                treeList1.RowHeight = newHeight;
            }

            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);

            _1HourTimer = new System.Windows.Forms.Timer();
            _1HourTimer.Tick += _1HourTimer_Elapsed;
            var timerInterval = 55;
            _1HourTimer.Interval = (int)TimeSpan.FromMinutes(timerInterval).TotalMilliseconds;
            _1HourTimer.Tag = 0;
            _1HourTimer.Start();

            _searchTermSyncTimer = new System.Windows.Forms.Timer();

            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
            _searchTermSyncTimer.Tick += _SearchTermSyncTimer_Elapsed;

            Debug.WriteLine(DateTime.Now.ToString("T") + "**Shown");

            if (Debugger.IsAttached || ProgramState.SerialNumber.StartsWith("ROMA"))
                Program.AffiliateOff = true;
            if (Program.FirstVisit)
            {
                //Analytics.AddEvent("", "first_visit", 1);
                //Analytics.PostReport();
            }

            ImageTools.ImageDownloadLoop();

            //Hide Restocker columns
            this.cJobId.Visible = false;
            this.cRequiredQuantity.Visible = false;
            this.cPurchasedQuantity.Visible = false;
            ApplyExpansionStatesAfterLoad();
        }

        private void ShowAlertNotification(string alertCaption, string alertText)
        {
            // Check if running on Windows 7
            var isWindows7 = Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6; ;
            if (isWindows7)
            {
                var alert = new DevExpress.XtraBars.Alerter.AlertControl();
                alert.AllowHtmlText = true;
                alert.AutoHeight = true;
                alert.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
                alert.ShowPinButton = true;

                alert.Show(this, alertCaption, alertText);
            }
        }

        private bool AssignProfileCombobox()
        {
            var profileNames = CookieManager.GetProfileNamesFirefox();

            // Clear existing items and add new ones.
            repositoryItemComboBoxProfile.Items.Clear();
            repositoryItemComboBoxProfile.Items.AddRange(profileNames);

            // Check if there are any items in the ComboBox.
            if (repositoryItemComboBoxProfile.Items.Count == 0)
            {
                return false;
            }

            // Determine which item to select in the ComboBox.
            CookieProfile selectedItem;

            if (repositoryItemComboBoxProfile.Items.Count == 1)
            {
                // If there is only one item in the ComboBox, select that item.
                selectedItem = repositoryItemComboBoxProfile.Items[0] as CookieProfile;
            }
            else
            {
                // If there are multiple items, find the first one that matches the current profile.
                var currentProfile = CookieManager.Profile;
                selectedItem = repositoryItemComboBoxProfile.Items.OfType<CookieProfile>().FirstOrDefault(item => item.Profile == currentProfile?.Profile);
            }

            // If a matching profile is found or there's only one profile, set it as the edit value.
            if (selectedItem != null)
            {
                barEditItemProfile.EditValue = selectedItem;
                return true;
            }

            // Fallback: Set the first item as the edit value if no specific match is found.
            barEditItemProfile.EditValue = repositoryItemComboBoxProfile.Items[0];
            return true;
        }

        internal void ReporterDotsProgressChanged(object sender, byte e)
        {
            ShowDebugStats();
            RunningDots();
        }

        internal void _reporterItem_ProgressChanged(object sender, FoundItem e)
        {
            HandleNewItem(e);
        }

        internal void ReportLogTxt(object sender, string e)
        {
            memoEditErrorLog.Text += $"\r\n{e}";
            if (memoEditErrorLog.Text.Length > 1000000 || memoEditErrorLog.Lines.Length > 500)
                memoEditErrorLog.Text = "";
        }

        private void BrowserDocCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            var webBrowser = (WebBrowser)sender;
            if (webBrowser.DocumentText.Length > 0)
            {
                try
                {
                    WebView.SetZoom(webBrowser, UserSettings.BrowserFontSize);
                }
                catch (Exception ex)
                {
                    if (ProgramState.Isdebug)
                        MessageBox.Show(ex.Message);
                }
            }
        }

        private void MouseMoveResetIdle(object sender, MouseEventArgs e)
        {
            ProgramState.Idlesw.Restart();
        }

        private void Dgv1KeyDown(object sender, KeyEventArgs e)
        {
            var grView = (AdvBandedGridView)sender;
            try
            {
                if (e.KeyCode == Keys.Delete)
                {
                    if (grView.RowCount == 0)
                    {
                        _guiChanger.ClearItemInfoOnZeroRows();
                    }

                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("dgv1KeyDown: ", ex);
            }
        }

        [Obfuscation(Exclude = false,
            Feature = "preset(maximum);"
                      + "+anti debug;"
                      + "+anti dump;"
                      + "+anti ildasm;"
                      + "+anti tamper(key=dynamic);"
                      + "+constants;"
                      + "+ctrl flow;"
                      + "+invalid metadata;"
                      + "+ref proxy;"
                      + "-rename;")]

        #region eBay Accounts

        private static async Task IsTokenValid(EbayAccount account)
        {
            try
            {
                if (string.IsNullOrEmpty(account?.TokenPo) || Program.Sandbox)
                    return;

                var apiContextPlaceOffer = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, account.TokenPo);

                var tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
                if (tokenStatus == null)
                {
                    tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
                }

                if (tokenStatus?.Status != TokenStatusCodeType.Active)
                {
                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
                    EBayAccountsList.Remove(account);
                }
            }
            catch (ApiException ex)
            {
                if (string.IsNullOrEmpty(account?.UserName))
                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
                else
                    XtraMessageBox.Show($"Account: {account.UserName}\nRemoved from account list\nToken status: {ex.Message}");

                EBayAccountsList.Remove(account);
            }

            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("isTokenValid: ", ex);
            }
        }

        private void AddEbayAccount()
        {
            if (EBayAccountsList.Count > 0 && !LicenseUtility.CurrentLimits.MultipleEbayAccountsEnabled)
            {
                XtraMessageBox.Show(En_US.Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_);

                return;
            }

            var account = ShowAuthorizeOnEbay();
            if (account != null)
            {
                var isNewAccount = true;
                for (var i = 0; i < EBayAccountsList.Count; i++)
                {
                    if (EBayAccountsList[i].UserName == account.UserName)
                    {
                        EBayAccountsList[i] = account;
                        isNewAccount = false;

                        break;
                    }
                }

                if (isNewAccount)
                {
                    EBayAccountsList.Add(account);
                    Analytics.AddEvent("", "EbayAccountAdded", 1);
                }
            }

            SaveSettings();
        }

        public static EbayAccount ShowAuthorizeOnEbay(EbayAccount editAccount = null)
        {
            //AutoMeasurement.Client.TrackScreenView("Screen - Authorize on ebay");

            var authenticator = new Authenticator(editAccount);
            var authForm = new FormAuth(editAccount, authenticator);

            var result = authForm.ShowDialog();
            if (result == DialogResult.OK)
            {
                if (!string.IsNullOrEmpty(authForm.EbayAccount.RefreshToken))
                {
                    return authForm.EbayAccount;
                }

                if (string.IsNullOrEmpty(authForm.EbayAccount.UserName) || string.IsNullOrEmpty(authForm.EbayAccount.TokenPo))
                {
                    return null;
                }

                return authForm.EbayAccount;
            }

            return null;
        }

        #endregion eBay Accounts

        private bool isShutdownInitiated = false;
        private double checkoutInterval = 31;


        private async void ClosingMainForm(object sender, FormClosingEventArgs e)
        {
            try
            {
                PythonProcessManager.StopInternalSkuManagerScript();
            }
            catch (Win32Exception ex)
            {
            }
            catch (InvalidOperationException ex)
            {
            }
            catch (Exception ex)
            {
            }
            // Check if shutdown has already been initiated
            if (isShutdownInitiated)
            {
                // Shutdown is already in progress, so do nothing
                return;
            }

            // Indicate that shutdown process has been initiated
            isShutdownInitiated = true;

            // Prevent the form from closing immediately
            e.Cancel = true;
            Cursor.Current = Cursors.WaitCursor;
            Text = "uBuyFirst - Shutting down";

            try
            {
                // Disconnect from services asynchronously
                // Unsubscribe from WatchlistManager event
                if (_watchlistManager != null)
                {
                    _watchlistManager.ItemBecameAvailable -= WatchlistManager_ItemBecameAvailable;
                }

                _ablyClient.BeDisconnected(); // Ensure this is an async method
                await _mqttManager.Disconnect(); // Ensure this is an async method

                // Attempt to stop other background work with a timeout
                var stopTask = StopWorking(); // Assuming StopWorking is an async Task method
                var delayTask = Task.Delay(TimeSpan.FromSeconds(60));
                await Task.WhenAny(stopTask, delayTask);

                // Additional cleanup or checks can be performed here
            }
            catch (Exception ex)
            {
                // Log the exception or show a message to the user
                MessageBox.Show($"An error occurred while shutting down: {ex.Message}", "Shutdown Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Complete the shutdown process by closing the form without further cancellation
                // Perform this action on the UI thread

                // Check if the form's handle is created before invoking
                if (this.IsHandleCreated)
                {
                    this.Invoke(new Action(() =>
                    {
                        // It's safe to close the form now as the shutdown process has been completed or acknowledged with an error
                        this.Close(); // This call will not re-enter the closing logic due to the isShutdownInitiated flag
                    }));
                }
                else
                {
                    // Handle the case where the form's handle is not created
                    // Log this situation if necessary
                    // Ensure the program exits
                    Environment.Exit(0);
                }
            }
        }

        internal void ShowActivationWindow()
        {
            var dialog = new FormSubscription();
            dialog.ShowDialog(Instance);

            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
            if (dialog.DialogResult != DialogResult.OK)
                return;

            Cursor.Current = Cursors.WaitCursor;
            var ipManagerFolder = "C:\\ProgramData\\IPManager";
            if (Directory.Exists(ipManagerFolder))
            {
                try
                {
                    Directory.GetFiles(ipManagerFolder, "*").ToList().ForEach(System.IO.File.Delete);
                }
                catch (System.UnauthorizedAccessException ex)
                {
                    MessageBox.Show("Access to the file 'C:\\ProgramData\\IPManager\\IPManager-AF1F-CC05-A6D4-4A7D-9BB6-2F84-3BC4-NolanRaupp.config' is denied. Please close the program, manually delete this file, and start the program again.", "Permission Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
            LicenseUtility.TryDownloadExistingLicense(dialog.SerialNumber);

            try
            {
                var license = LicenseUtility.ReadGenuineLicenseFromFile();

                if (license == null)
                {
                    LicenseUtility.ActivateLicense(dialog.SerialNumber);
                }
                else
                {
                    XtraMessageBox.Show(En_US.Form1_ShowActivationWindow_Please__restart_the_application__);
                }
            }
            catch (Exception)
            {
                //ignored
            }

            Cursor.Current = Cursors.Default;
        }

        public static void ShowCustomColumnsWindow(object sender, EventArgs e)
        {
            var dialog = new FormSpecificsColumns();
            dialog.ShowDialog();
        }

        private void zoomTrackBarControl1_EditValueChanged_1(object sender, EventArgs e)
        {
            galleryControl1.Gallery.Groups.Last().Gallery.ImageSize = new Size(zoomTrackBarPictures.Value, zoomTrackBarPictures.Value);
            LastSliderValueSmall = zoomTrackBarPictures.Value;
        }

        private void barButtonClear_ItemClick(object sender, ItemClickEventArgs e)
        {
            _guiChanger.ClearFoundItems();
        }

        private void barButtonReport_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (_formReportDialog != null)
            {
                _formReportDialog.WindowState = FormWindowState.Normal;
                _formReportDialog.Focus();
            }

            else
            {
                _formReportDialog = new FormReportDialog();
                // formReportDialog.MdiParent = this;
                _formReportDialog.FormClosed += (o, ea) => { _formReportDialog = null; };
                _formReportDialog.Show();
            }

            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
        }

        private void barButtonBuy_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (FocusRouter.FocusedGridView == null)
            {
                return;
            }

            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();

            if (selectedRows.Length == 0)
                return;

            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);

            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
        }

        private void barButtonSubscriptionInfo_ItemClick(object sender, ItemClickEventArgs e)
        {
            ShowActivationWindow();
        }

        private void barButtonItem3_ItemClick(object sender, ItemClickEventArgs e)
        {
            var dialog = new FormAbout();
            dialog.ShowDialog();
        }

        private void btnClearLogs_Click(object sender, EventArgs e)
        {
            Stat._errorsCount = 0;
            memoEditErrorLog.Text = "";
            barStaticErrorsVal.Caption = "Errors: 0";
        }

        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
        {
            Process.Start(hyperlinkLabelOpoenLogFolder.Tag.ToString());
        }

        private void dockDescription_CustomButtonClick(object sender, ButtonEventArgs e)
        {
            if (e.Button.Properties.Tag.ToString() == "BackgroundColor")
            {
                var result = colorDialog1.ShowDialog();
                if (result == DialogResult.OK)
                {
                    // Set form background to the selected color.
                    UserSettings.BrowserBg = WebView.HexConverter(colorDialog1.Color);
                    _guiChanger.SetBrowserDocumentText("");
                }
            }

            if (e.Button.Properties.Tag.ToString() == "IncreaseFont")
            {
                UserSettings.BrowserFontSize += 10;

                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
            }

            if (e.Button.Properties.Tag.ToString() == "DecreaseFont")
            {
                UserSettings.BrowserFontSize = UserSettings.BrowserFontSize - 10;
                if (UserSettings.BrowserFontSize < 10)
                    UserSettings.BrowserFontSize = 10;

                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
            }
        }

        #region Browser

        private void zoomTrackBarBrowser_EditValueChanged(object sender, EventArgs e)
        {
            if (sender is ZoomTrackBarControl zoomTrackBar)
            {
                UserSettings.BrowserFontSize = zoomTrackBar.Value;
                if (webBrowser1.DocumentText.Length > 0)
                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
            }

            if (sender is BarEditItem repositoryZoomTrackBar)
            {
                UserSettings.BrowserFontSize = int.Parse(repositoryZoomTrackBar.EditValue.ToString());
                if (webBrowser1.DocumentText.Length > 0)
                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
            }
        }

        private void colorPickBrowser_EditValueChanged(object sender, EventArgs e)
        {
            if (sender is BarEditItem colorEdit)
            {
                UserSettings.BrowserBg = WebView.HexConverter((Color)colorEdit.EditValue);
                btnBrowserSettings.Appearance.BackColor = (Color)colorEdit.EditValue;
                _guiChanger.SetBrowserDocumentText("");
            }

            if (sender is ColorPickEdit colorPickEdit)
            {
                UserSettings.BrowserBg = WebView.HexConverter(colorPickEdit.Color);
                btnBrowserSettings.Appearance.BackColor = colorPickEdit.Color;
                _guiChanger.SetBrowserDocumentText("");
            }
        }

        #endregion


        private void _1secTimer_Elapsed(object sender, EventArgs e)
        {
            var timer = (System.Windows.Forms.Timer)sender;
            var elapsedSec = (int)timer.Tag + 1;
            timer.Tag = elapsedSec;

            RowStatusUpdater.RefreshRowsStatus();

            if (DateTime.UtcNow.Second % 3 == 0)
                _synchronizationContext.Send(state => Instance.ShowDebugStats(), null);

            if (ProgramState.Idlesw.Elapsed.TotalSeconds > 20 && Upgrader.ImmediateUpgradeAvailable)
            {
                Upgrader.ImmediateUpgradeAvailable = false;
                Upgrader.UpgradeImmediately();
            }

            GoogleAnalytics?.SendSearchStatsPeriodic();

            var needRestart = Instance._ablyClient.NeedRestart();

            if ((elapsedSec + 15) % 60 == 0 && needRestart)
            {
                Debug.WriteLine($"Ably: {elapsedSec} Force disconnect");
                Instance._ablyClient.Disconnect();
            }

            if (elapsedSec % 60 == 0 && needRestart)
            {
                Debug.WriteLine($"Ably: {elapsedSec} Force connect");
                if (!ConnectionConfig.MQTTEnabled)
                    Instance.StartPushClients();
            }

            RefreshWatchListRemainingTime();
            UpdateRestockCooldownStatus();

            // Process checkout queue every 5 seconds
            if (elapsedSec % 5 == 0)
            {
                ProcessCheckoutQueue();
            }
        }

        private void _1HourTimer_Elapsed(object sender, EventArgs e)
        {
            _updater?.CheckProgramUpdateAvailable();
            ConnectionConfig.SetApiConfig().ConfigureAwait(false);
            if (!ConnectionConfig.TradingAPIEnabled)
            {
                barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
            }

            LicenseUtility.PeriodicLicenseCheck();
        }

        /// <summary>
        /// Static variable to track the last checkout time for 40-second interval enforcement
        /// </summary>
        private static DateTime _lastCheckoutTime = DateTime.MinValue;

        /// <summary>
        /// Semaphore to control checkout queue processing - ensures only one item can be
        /// in the processing pipeline at a time, preventing multiple items from having
        /// CreatingSession status simultaneously
        /// </summary>
        private static readonly SemaphoreSlim _checkoutQueueSemaphore = new(1, 1);

        /// <summary>
        /// Processes the checkout queue by finding items with CheckoutPending status
        /// and processing one item if 40 seconds have elapsed since the last checkout.
        /// Uses a semaphore-based approach to ensure only one item can be in the
        /// processing pipeline at a time.
        /// </summary>
        private void ProcessCheckoutQueue()
        {
            try
            {
                // Check if restock functionality is enabled
                if (!UserSettings.RestockerEnabled)
                    return;

                // Check if we're in captcha cooldown
                if (CaptchaCooldownManager.IsInCooldown)
                    return;

                // Check if 40 seconds have passed since last checkout
                var timeSinceLastCheckout = DateTime.UtcNow - _lastCheckoutTime;

                if (timeSinceLastCheckout.TotalSeconds < checkoutInterval)
                {
                    var remainingSeconds = checkoutInterval - timeSinceLastCheckout.TotalSeconds;
                    Debug.WriteLine($"Checkout queue: Waiting {remainingSeconds:F1} more seconds before next item processing");
                    return;
                }

                // Find all items with CheckoutPending status across all views
                var pendingItems = GetPendingCheckoutItems();
                if (!pendingItems.Any())
                    return;

                // Sort by found time (earliest first) and take the first item
                var nextItem = pendingItems
                    .OrderBy(i => i.ItemPricing?.ItemPrice?.Value ?? 0d)
                    .FirstOrDefault();

                // Process the item asynchronously with semaphore protection
                _ = Task.Run(async () => await ProcessSingleCheckoutItemAsync(nextItem));

                // Note: _lastCheckoutTime is now updated in ProcessSingleCheckoutItemAsync
                // based on the actual purchase result to avoid unnecessary delays for skipped operations
            }
            catch (Exception ex)
            {
                // Error in ProcessCheckoutQueue - continue processing
            }
        }

        /// <summary>
        /// Gets all items with CheckoutPending status from all views, removing duplicates by ItemID
        /// </summary>
        private List<DataList> GetPendingCheckoutItems()
        {
            var pendingItems = new List<DataList>();
            var seenItemIds = new HashSet<string>();

            try
            {
                foreach (var view in ResultsView.ViewsDict.Values)
                {
                    var dataTable = (DataTable)view.DataSource;
                    if (dataTable == null) continue;

                    for (int i = 0; i < dataTable.Rows.Count; i++)
                    {
                        var row = dataTable.Rows[i];
                        if (row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                            continue;

                        var dataList = (DataList)row["Blob"];
                        if (dataList.ItemStatus == Data.ItemStatus.CheckoutPending)
                        {
                            // Only add if we haven't seen this ItemID before (handle duplicates)
                            if (seenItemIds.Add(dataList.ItemID))
                            {
                                pendingItems.Add(dataList);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting pending checkout items: {ex.Message}");
            }

            return pendingItems;
        }

        /// <summary>
        /// Processes a single checkout item asynchronously and handles duplicates.
        /// Uses semaphore protection to ensure only one item can be in the processing
        /// pipeline at a time, preventing multiple items from having CreatingSession status.
        /// </summary>
        private async Task ProcessSingleCheckoutItemAsync(DataList dataList)
        {
            var itemId = dataList.ItemID;

            // Acquire the checkout queue semaphore to ensure only one item processes at a time
            await _checkoutQueueSemaphore.WaitAsync();

            try
            {
                // Now that we have exclusive access, update status to indicate processing has started
                _synchronizationContext.Send(_ => dataList.SetStatus(Data.ItemStatus.CreatingSession), null);

                // Also update status for all duplicate items (same ItemID) to prevent multiple processing
                _synchronizationContext.Send(_ => UpdateDuplicateItemsStatus(itemId, Data.ItemStatus.CreatingSession), null);

                // Create RestockFilterAction instance to handle the purchase
                var restockAction = new RestockFilterAction();

                // Use the stored filter alias if available, otherwise fall back to generic identifier
                var filterAlias = !string.IsNullOrEmpty(dataList.TriggeringFilterAlias)
                    ? dataList.TriggeringFilterAlias
                    : "checkout-queue";

                // Execute the purchase attempt
                var result = await restockAction.TryPurchaseItemAsync(dataList, filterAlias);

                // Update checkout delay timer based on whether this was an actual purchase attempt
                if (ShouldApplyCheckoutDelay(result))
                {
                    _lastCheckoutTime = DateTime.UtcNow;
                }
                else
                {
                    // For skipped items, immediately trigger processing of the next item
                    // This bypasses the 5-second timer and allows rapid processing of quantity-already-reached items
                    // The semaphore protection ensures only one item can be processed at a time
                    _ = Task.Run(() =>
                    {
                        // Small delay to ensure current processing completes
                        Task.Delay(100).Wait();

                        // Trigger immediate re-processing on the main thread
                        _synchronizationContext.Post(_ => ProcessCheckoutQueue(), null);
                    });
                }

                // Update UI on the main thread after purchase attempt
                _synchronizationContext.Send(_ =>
                {
                    if (result.Success)
                    {
                        // Determine the correct final status based on the checkout result
                        // This ensures TestPurchase status is preserved correctly
                        var finalStatus = DetermineFinalStatusAfterSuccessfulCheckout(dataList);

                        // Update all duplicate items to the same final status
                        UpdateDuplicateItemsStatus(itemId, finalStatus);

                        // Refresh UI to show the changes
                        treeList1.RefreshDataSource();
                        UpdateTodaySpentDisplay();
                    }
                    else
                    {
                        // If purchase failed, reset status to Active for all duplicates so they can be retried later
                        UpdateDuplicateItemsStatus(itemId, Data.ItemStatus.Active);
                        Debug.WriteLine($"Checkout failed for item {itemId}: {result.Message}");
                    }
                }, null);
            }
            catch (Exception ex)
            {
                // Reset status on error for all duplicates
                _synchronizationContext.Send(_ => UpdateDuplicateItemsStatus(dataList.ItemID, Data.ItemStatus.Active), null);
            }
            finally
            {
                // Always release the checkout queue semaphore to allow next item to process
                _checkoutQueueSemaphore.Release();
            }
        }

        /// <summary>
        /// Updates the status of all items with the same ItemID across all views
        /// </summary>
        private void UpdateDuplicateItemsStatus(string itemId, Data.ItemStatus newStatus)
        {
            try
            {
                foreach (var view in ResultsView.ViewsDict.Values)
                {
                    var dataTable = (DataTable)view.DataSource;
                    if (dataTable == null) continue;

                    for (int i = 0; i < dataTable.Rows.Count; i++)
                    {
                        var row = dataTable.Rows[i];
                        if (row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                            continue;

                        var dataList = (DataList)row["Blob"];
                        if (dataList.ItemID == itemId)
                        {
                            dataList.SetStatus(newStatus);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating duplicate items status for {itemId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines the correct final status after a successful checkout based on the order's checkout status
        /// This ensures TestPurchase status is preserved correctly and real purchases are marked as Sold
        /// </summary>
        private Data.ItemStatus DetermineFinalStatusAfterSuccessfulCheckout(DataList dataList)
        {
            try
            {
                // Check the order's checkout status to determine the correct final item status
                if (dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    return Data.ItemStatus.TestPurchase;
                }
                else if (dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess)
                {
                    return Data.ItemStatus.Sold;
                }
                else
                {
                    // Fallback to current item status if checkout status is unclear
                    return dataList.ItemStatus;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error determining final status for item {dataList.ItemID}: {ex.Message}");
                // Fallback to current item status on error
                return dataList.ItemStatus;
            }
        }

        /// <summary>
        /// Determines if a purchase execution result represents an actual purchase attempt
        /// that should trigger the 40-second delay, or a skipped operation that should not.
        /// </summary>
        /// <param name="result">The purchase execution result to evaluate</param>
        /// <returns>True if the result represents an actual purchase attempt requiring delay, false for skipped operations</returns>
        private static bool ShouldApplyCheckoutDelay(PurchaseExecutionResult result)
        {
            if (result == null)
                return false;

            // Successful purchases always require delay
            if (result.Success)
                return true;

            // Daily limit exceeded is a special skip condition - no delay needed
            if (result.IsDailyLimitExceeded)
                return false;

            // For failed purchases, we need to distinguish between actual purchase attempts
            // that failed (requiring delay) and operations that were skipped before attempting purchase
            // We can identify skipped operations by checking for common skip messages
            var message = result.Message ?? string.Empty;
            var skipIndicators = new[]
            {
                "Required quantity already reached",
                "Purchase requirement already fulfilled",
                "Keyword has no restock configuration",
                "Credit card payment is not enabled",
                "Item is not available for purchase",
                "Captcha cooldown active",
                "Purchase rate limited",
                "Item is already queued or being processed",
                "Purchase execution is disabled",
                "Could not find keyword",
                "Restock functionality is disabled"
            };

            // If the message contains any skip indicators, no delay is needed
            foreach (var indicator in skipIndicators)
            {
                if (message.IndexOf(indicator, StringComparison.OrdinalIgnoreCase) >= 0)
                    return false;
            }

            // For all other failure cases (actual purchase attempts that failed), apply delay
            return true;
        }

        private async void _SearchTermSyncTimer_Elapsed(object sender, EventArgs e)
        {
            if (!UserSettings.SyncSearchTermsEnabled)
                return;


            var url = UserSettings.SyncSearchTermsUrl;
            if (string.IsNullOrEmpty(url))
                return;

            if (Regex.IsMatch(url, SearchTermFetcher.GoogleSpreadsheetRegex))
                url = SearchTermFetcher.GetCsvExportUrl(url);

            var filePath = await SearchTermFetcher.DownloadKeywordsFileAsync(url);
            if (string.IsNullOrEmpty(filePath))
                return;

            var searchTermsFileContent = File.ReadAllText(filePath);
            var searchTermFileHash = Helpers.Str2Guid(searchTermsFileContent).ToString();
            if (searchTermFileHash != UserSettings.SyncSearchTermsFileHash)
            {
                UserSettings.SyncSearchTermsFileHash = searchTermFileHash;
                ImportKeywordsFromFile(filePath);
                if (_searchService is { Running: true })
                {
                    await StopWorking();
                    StartWorking();
                }
            }
        }

        private async void InitEbayAccounts()
        {
            await Task
                .Run(async () =>
                {
                    var ebayAccounts = EBayAccountsList.ToList();
                    foreach (var ebayAccount in ebayAccounts)
                    {
                        await IsTokenValid(ebayAccount).ContinueWith(t => Debug.WriteLine("Account checked"));
                    }

                    EBayAccountInitCompleted = true;
                })
                .ContinueWith(t => { });

            Debug.WriteLine("EBayAccountInitCompleted = true");
        }

        private void InitUpdater()
        {
            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Never;
            _updater = new Updater();
        }

        private void CreateDefaultSearches()
        {
            if (_ebaySearches.ChildrenCore.Count == 0)
            {
                NewEbaySearch("iPhone", "iPhone 14 -(128,256,128gb,256gb) -verizon", "9355");
                NewEbaySearch("GPU 4090", "4090 (MSI,Asus,Gigabyte)", "175673");
                NewEbaySearch("Lenovo laptop", "Lenovo ThinkPad, Lenovo IdeaPad", "175672");
                SetTreeListCheckboxesState();
            }

            for (var i = 0; i < treeList1.Nodes.Count; i++)
                SetNodeChecked(treeList1.Nodes[i]);
        }

        private async void InitBGtasks()
        {
            _checkItemsStatus = new GetItemsStatus();
            _checkItemsStatus.StartCheckStatusLoop();
            StartTelegram();
            StartPushbullet();
            barStaticItemTimeDiffText.Caption = $@"eBay - Local time: {await Task.Run(TimeSync.GetTimeSyncDifference)} sec";
        }

        private void StartPushbullet()
        {
            if (string.IsNullOrEmpty(_pushbullet?.Token))
                return;

            PushBulletListen();
        }

        private void StartTelegram()
        {
            if (string.IsNullOrEmpty(_telegramSender?.TelegramBotID))
                return;

            _telegramSender?.StartListening();
        }

        private void SetupGUI()
        {
            layoutControl1.SetDefaultLayout();
            layoutControlForPicture.SetDefaultLayout();
            ribbonControl1.Minimized = true;
            barStaticBuildVersion.Caption = En_US.Form1_Form1_Build__v_ + ProgramState.UBFVersion;
            hyperlinkLabelOpoenLogFolder.Text = $"App folder: {Folders.Settings}";
            hyperlinkLabelOpoenLogFolder.Tag = $"{Folders.Settings}";
            //if (!_isdebug)
            splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
            if (Program.AffiliateOff)
                barStaticErrorsVal.Caption = "Affiliate Off";
        }

        private void ActiveLookAndFeel_StyleChanged(object sender, EventArgs e)
        {
            ChangePanelHeader();

            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                FormatRuleManager.ApplyGridFormatRules(grView);
            }
        }

        private static void ChangePanelHeader()
        {
            var panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindow];
            if (panelHeader != null)
            {
                panelHeader.ContentMargins.Top = 1;
                panelHeader.ContentMargins.Bottom = 1;
            }

            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinTabHeader];
            if (panelHeader != null)
            {
                panelHeader.ContentMargins.Top = 1;
                panelHeader.ContentMargins.Bottom = 1;
            }

            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindowBorder];

            panelHeader.ContentMargins.Top = 1;
            panelHeader.ContentMargins.Bottom = 1;
            foreach (DockPanel panel in Instance.dockManager1.Panels)
            {
                panel.Invalidate();
                panel.Refresh();
            }

            LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
        }

        private void toolTipController2_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            ShowTooltipThumbnail(e);
        }

        private void ShowTooltipThumbnail(ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            if (e.SelectedControl != gridControl1)
                return;

            ToolTipControlInfo info = null;
            var sTooltip1 = new SuperToolTip();
            try
            {
                if (!(gridControl1.GetViewAt(e.ControlMousePosition) is GridView view))
                    return;

                var hi = view.CalcHitInfo(e.ControlMousePosition);
                if (hi.HitTest == GridHitTest.RowCell && hi.Column.FieldName == "Thumbnail")
                {
                    info = new ToolTipControlInfo(new CellToolTipInfo(hi.RowHandle, hi.Column, "cell"), hi.RowHandle + "," + hi.Column);
                    if (view.GetRowCellValue(hi.RowHandle, "Thumbnail") is Bitmap cellIm)
                    {
                        var item1 = new ToolTipItem();
                        item1.Image = cellIm;
                        sTooltip1.Items.Add(item1);
                    }

                    info = new ToolTipControlInfo(hi.RowHandle + "," + hi.Column, "");
                    info.SuperTip = sTooltip1;
                }
            }
            catch (Exception)
            {
                // ignored
            }
            finally
            {
                e.Info = info;
            }
        }

        #region InfoPanel

        private void lcItemID_Click(object sender, EventArgs e)
        {
            object tag;
            if (!(sender is LabelControl labelControl))
            {
                if (!(sender is LayoutControlItem layoutControlItem))
                    return;

                tag = layoutControlItem.Tag;
            }
            else
            {
                tag = labelControl.Tag;
            }

            var ebayAccount = ((DataList)tag)?.EbayAccount;
            if (ebayAccount != null)
            {
                var url = ((DataList)tag).GetAffiliateLink();
                if (url != null)
                    Browser.OpenAffiliateLink((DataList)tag);
            }
        }

        private void dockItemProperties_CustomButtonClick(object sender, ButtonEventArgs e)
        {
            if (e.Button.Properties.Tag.ToString() == "ItemDesciptionOptions")
            {
                popupMenuItemDetails.ShowPopup(MousePosition);
            }
        }

        private void SetLayoutControlFont(Font f, int delta)
        {
            if (layoutControl1.Root.AppearanceItemCaption.FontSizeDelta < 0)
                layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = 0;

            layoutControl1.Root.AppearanceItemCaption.Font = new Font(f.FontFamily, (float)8.25, f.Style);
            layoutControl1.Appearance.Control.FontSizeDelta = delta;
            lcTitle.Font = new Font(lcTitle.Font.FontFamily, layoutControl1.Appearance.Control.Font.Size + 3, FontStyle.Bold);
        }

        private void btnEditItemProperties_Click(object sender, EventArgs e)
        {
            popupMenuItemDetails.ShowPopup(MousePosition);
        }

        private void btnEditPictureProperties_Click(object sender, EventArgs e)
        {
            if (lcipanelPicturesSettingControl.Visibility != DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
            {
                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            }
            else
            {
                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
            }
        }

        #endregion

        #region Buying buttons

        private void barButtonMakeOffer_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (FocusRouter.FocusedGridView == null)
            {
                return;
            }

            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();

            if (selectedRows.Length == 0)
                return;

            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);

            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
        }

        private void btnMakeOffer_Click(object sender, EventArgs e)
        {
            if (FocusRouter.FocusedGridView == null)
            {
                return;
            }

            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();

            if (selectedRows.Length == 0)
                return;

            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);

            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
        }

        private void panelBuyButton_Click_1(object sender, EventArgs e)
        {
            if (FocusRouter.FocusedGridView == null)
            {
                return;
            }

            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();

            if (selectedRows.Length == 0)
                return;

            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);

            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
        }

        #endregion

        private void barButtonClear_ItemDoubleClick(object sender, ItemClickEventArgs e)
        {
            var dialogResult = XtraMessageBox.Show(this, "Clear results?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
            if (dialogResult == DialogResult.Yes)
            {
                ClearOldResults();
                barButtonClear.Caption = "Clear results " + DateTime.Now.ToString("HH:mm:ss");
            }
        }

        private void ClearOldResults()
        {
            // Clear AI processing queue first to stop any pending AI tasks
            RequestQueueManager.Instance.ClearAiProcessingQueue();

            _guiChanger.ClearFoundItems();
            SearchService.Old.Clear();
            CacheManager.ClearCache();
            _ebaySearches.ChildrenCore.ForEach(s => s.InitialSearchCount = 0);
            _telegramSender?.ClearQueue();
        }

        private void repositoryItemCheckedComboBoxEditCondition_CustomDisplayText(object sender,
            CustomDisplayTextEventArgs e)
        {
            var term = e.Value as string;
            if (string.IsNullOrEmpty(term))
            {
                e.DisplayText = "Any condition";
            }
        }

        private void btnClearFilter_Click(object sender, EventArgs e)
        {
            popupContainerControl1.OwnerEdit?.CancelPopup();
        }

        private void barStaticErrorsVal_ItemDoubleClick(object sender, ItemClickEventArgs e)
        {
            //XtraMessageBox.Show("Directx: " + GridDirectXEnabled(gridControl1).ToString());
            if (splitContainerControl2.PanelVisibility == SplitPanelVisibility.Both)
            {
                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
            }
            else
            {
                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Both;
            }
        }

        private void UpdateRequestCfg()
        {
            if (!Visible)
                return;

            var config = SearchConfigManager.Instance;

            config.FindReqMaxThreads = Convert.ToInt32(spinEditFindReqMaxThreads.Value);
            config.GetItemDetailsReqMaxThreads = Convert.ToInt32(spinEditGetItemDetailsMaxThreads.Value);
            // Note: The semaphore is now managed internally by SearchConfigManager

            config.DownloadAvatars = chkDownloadAvatars.Checked;
            config.DownloadOtherImages = chkDownloadOtherImages.Checked;
            config.DownloadDescription = chkDownloadDescription.Checked;
            config.UpdateItemStatus = chkUpdateItemStatusFor2Min.Checked;
            config.EnabledApi = checkUseAPI.Checked;
            config.EnabledRss = checkUseRSS.Checked;
            config.EnabledRss2 = checkUseRSS2.Checked;
            config.ShowSoldItems = checkEditShowSoldItems.Checked;
            config.WhiteSpaceAnalyzer = checkEditWhitespaceTokenizer.Checked;

            // Config manager will validate settings automatically
            config.ValidateConfig();

            if (!config.EnabledApi && !config.EnabledRss && !config.EnabledRss2)
            {
                MessageBox.Show("Please, enable at least one option - API, RSS or RSS2");
            }
        }

        private void _requestCfgChanged(object sender, EventArgs e)
        {
            UpdateRequestCfg();
            SetWhiteSpaceAnalyzer(SearchConfigManager.Instance.WhiteSpaceAnalyzer);
        }

        private static void SetWhiteSpaceAnalyzer(bool requestsCfgWhiteSpaceAnalyzer)
        {
            KeywordHelpers.SetAnalyzerType(requestsCfgWhiteSpaceAnalyzer);
        }

        private void Info_Click(object sender, EventArgs e)
        {
            XtraMessageBox.Show(@"Find request speed - How many find request to eBay servers per second uBuyFirst actually makes

GetItem queue - after uBuyFirst found a new item, it needs to make one more request to eBay server to get this item's details. If there are many new items, they placed into a queue.

GetItem threads - how many parallel GetItem details requests uBuyFirst can make.

Thumbs - placed into a queue. You may disable it if you don't need them.

Other images - gallery images. You may disable it if you don't need them.

Status update - after new item were added to a grid uBuyFirst checks for 2 minutes whetheer the item where sold. 20 items updates per request. If you have 10 items in queue then 10x20=200 items currently updating its status.

Download description - you may uncheck this if you don't use Description in your filters and Sub Searches.

Use API, RSS, RSS2 - we can look for new items in these 3 sources. Sometimes some of them faster then another.

Show sold items - look in the sold items archive for items that were sold before ubuyfirst found them.
");
        }

        #region Gallery

        private void galleryControl1_Gallery_ItemDoubleClick(object sender, GalleryItemClickEventArgs e) => _picManager.GetLargeImages(e.Item, this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);

        private void galleryControl1_Gallery_GalleryItemHover(object sender, GalleryItemEventArgs e)
        {
            var galleryItem = e.Item;
            if (e.Item.Hint == "Error")
            {
                var galleryItemGroupCollection = ((GalleryControlGallery)sender).GalleryControl.Gallery.Groups;
                var galleryItemGroup = galleryItemGroupCollection.Last();
                var galleryItemCollection = galleryItemGroup.Items;
                var pictureUrls = new List<string>();
                foreach (GalleryItem? item in galleryItemCollection)
                {
                    var picUrl = item?.Tag?.ToString();
                    if (!string.IsNullOrEmpty(picUrl))
                        if (picUrl != null)
                        {
                            pictureUrls.Add(picUrl);
                        }
                }

                GridViewEvents.GuiChanger.ClearGalleryControl();
                ImageCache.Inst.ClearCache();
                GridViewEvents.GuiChanger.UpdatePicPanel(pictureUrls.ToList(), new CancellationToken(false));
            }


            _picManager.GetLargeImages(galleryItem, this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);
        }

        #endregion

        #region Tray Icon

        private void TrayIcon_Click(object sender, MouseEventArgs e)
        {
            _trayManager.SetTrayLabel();
            if (e.Button == MouseButtons.Left)
                _trayManager.SwitchMinimizedState();
        }

        private void toolStripMenuItemExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void toolStripMenuItemShow_Click(object sender, EventArgs e)
        {
            _trayManager.SwitchMinimizedState();
        }

        #endregion Tray Icon

        public void ShowUpdateNotification()
        {
            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Always;
        }

        private void BarButtonRestartOnUpdate_ItemClick(object sender, ItemClickEventArgs e)
        {
            Upgrader.UpgradeImmediately();
        }

        private void hyperLinkLabel_Click(object sender, EventArgs e)
        {
            var label = (HyperlinkLabelControl)sender;
            Process.Start(label.Tag.ToString());
        }

        private void repositoryItemViews_AddingMRUItem(object sender, AddingMRUItemEventArgs e)
        {
            return;

            if (UserSettings.CanShowEbaySearchEditor)
                return;

            if (e.AddReason == MRUItemAddReason.Internal)
                return;

            if (!(sender is MRUEdit mruEdit))
                return;

            if (!(mruEdit.Parent is TreeList treeList))
                return;

            if (!(treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find))
                return;

            var text = $"Create '{e.Item}' View?\nItems for this eBay search will be displayed in this View";
            var dialogResult = XtraMessageBox.Show(treeList, text, @"Create View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            if (dialogResult != DialogResult.Yes)
            {
                e.Cancel = true;
                mruEdit.SelectedItem = focusedKw2Find.ViewName;
                mruEdit.CancelPopup();
                treeList1.CancelCurrentEdit();
                MruManager.DisableEdit(repositoryItemViews);

                return;
            }

            UserSettings.CanShowEbaySearchEditor = true;
            var newViewName = e.Item.ToString();
            ResultsView.CreateView(newViewName);
            GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
            UserSettings.CanShowEbaySearchEditor = false;
            ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);
            MruManager.DisableEdit(repositoryItemViews);
        }

        private void repositoryItemViews_RemovingMRUItem(object sender, RemovingMRUItemEventArgs e)
        {
            var removedViewName = e.Item.ToString();
            if (removedViewName == "Results")
            {
                XtraMessageBox.Show(this, "Sorry, you can't remove default view 'Results'");
                e.Cancel = true;
                return;
            }

            var text = $"Delete '{e.Item}' View?\nAll eBay Searches in this view will be assigned to 'Results' View";
            var dialogResult = XtraMessageBox.Show(text, @"Delete View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
            if (dialogResult != DialogResult.Yes)
            {
                e.Cancel = true;
            }
            else
            {
                ResultsView.AssignViewForSearches(removedViewName, "Results", _ebaySearches.ChildrenCore);

                for (var i = 0; i < treeList1.Nodes.Count; i++)
                {
                    if (treeList1.GetDataRecordByNode(treeList1.Nodes[i]) is Keyword2Find)
                    {
                        if (treeList1.Nodes[i].GetValue("View").ToString() == removedViewName)
                        {
                            treeList1.Nodes[i].SetValue("View", @"Results");
                        }
                    }
                }

                repositoryItemViews.Items.Remove(e.Item);
                var mruEdit = (MRUEdit)sender;
                if (mruEdit.Parent is TreeList treelist2)
                    if (treelist2.GetDataRecordByNode(treelist2.FocusedNode) is Keyword2Find focusedKw2Find)
                    {
                        mruEdit.SelectedItem = focusedKw2Find.ViewName;
                        mruEdit.EditValue = focusedKw2Find.ViewName;
                        mruEdit.Text = focusedKw2Find.ViewName;
                    }

                var obsoletePanel = dockManager1.Panels.FirstOrDefault(p => p.Tag?.ToString() == removedViewName);
                obsoletePanel?.Dispose();
                ResultsView.RemoveFromViewList(removedViewName);
                mruEdit.Properties.Items.Remove(e.Item);
                treeList1.Refresh();
            }
        }

        private static void RepositoryItemViews_SelectedIndexChanged(object sender, EventArgs e)
        {
            var mruEdit = (MRUEdit)sender;

            if (mruEdit.SelectedIndex == -1)
                return;

            if (!(mruEdit.Parent is TreeList treeList))
                return;

            if (treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find)
            {
                var viewName = mruEdit.SelectedItem.ToString();
                ResultsView.AssignViewToSearch(viewName, focusedKw2Find);
            }
        }

        private void RepositoryItemViews_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            var mruEdit = (MRUEdit)sender;
            if (e.Button.Caption == "Create View")
                ShowAddViewDialog(mruEdit);

            return;

            if (e.Button.Caption == "Create View")
            {
                switch (mruEdit.Properties.TextEditStyle)
                {
                    case TextEditStyles.Standard: //Disable
                        MruManager.DisableEdit(repositoryItemViews);

                        break;
                    case TextEditStyles.DisableTextEditor: //Enable
                        MruManager.EnableEdit(repositoryItemViews);
                        treeList1.ShowEditor();
                        treeList1.ActiveEditor.Select();

                        break;
                }
            }

            if (e.Button.Caption == "Cancel") //Disable
            {
                treeList1.CancelCurrentEdit();
                MruManager.DisableEdit(repositoryItemViews);
            }
        }

        private void ShowAddViewDialog(MRUEdit mruEdit)
        {
            var args = new XtraInputBoxArgs();
            // set required Input Box options
            args.Caption = "Create View panel";
            args.Prompt = "View name";
            args.DefaultButtonIndex = 0;

            // initialize a DateEdit editor with custom settings
            var viewNameEdit = new TextEdit();
            //viewNameEdit.Properties.CalendarView = DevExpress.XtraEditors.Repository.CalendarView.TouchUI;
            //viewNameEdit.Properties.Mask.EditMask = "MMMM d, yyyy";
            args.Editor = viewNameEdit;

            // a default DateEdit value
            if (!(treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find focusedKw2Find))
                return;

            args.DefaultResponse = focusedKw2Find.Alias;
            // display an Input Box with the custom editor
            var show = XtraInputBox.Show(args);
            if (show != null)
            {
                var newViewName = show.ToString();
                ResultsView.CreateView(newViewName);
                GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
                UserSettings.CanShowEbaySearchEditor = false;
                ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);

                MruManager.AddMissingMruItem(newViewName, repositoryItemViews);
                //  mruEdit.Properties.Items.Add(newViewName);

                foreach (var treeList1Node in treeList1.Selection)
                {
                    treeList1.RefreshNode(treeList1Node);
                }

                //mruEdit.SelectedItem = newViewName;
                //   mruEdit.SelectedIndex = 0;
                //     mruEdit.EditValue = newViewName;
            }

            mruEdit.SelectedItem = focusedKw2Find.ViewName;
            //MruManager.DisableEdit(repositoryItemViews);
        }

        private void repositoryItemCheckedComboBoxEditListingType_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
        {
            var term = e.Value as string;
            if (string.IsNullOrEmpty(term))
            {
                e.DisplayText = "Buy It Now";
            }
            else
            {
                if (term.Contains(ListingType.BuyItNow.ToString()) && term.Contains(ListingType.AuctionsEndingNow.ToString()) && term.Contains(ListingType.AuctionsStartedNow.ToString()))
                {
                    e.DisplayText = "Any";
                }
            }
        }

        private void barEditItemAutoSelect_EditValueChanged(object sender, EventArgs e)
        {
            TopRowFocus.TopRowFocusInterval = double.Parse(barEditItemAutoSelect.EditValue.ToString(), CultureInfo.InvariantCulture);
        }

        private void btnHighlightWords_Click(object sender, EventArgs e)
        {
            var formHighlightWords = new FormHighlightWords();
            formHighlightWords.ShowDialog(this);
            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
            SaveSettings();
        }

        private void btnBrowserSettings_Click(object sender, EventArgs e)
        {
            if (flyoutPanelBrowser.IsPopupOpen)
                flyoutPanelBrowser.HidePopup();
            else
            {
                flyoutPanelBrowser.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
                flyoutPanelBrowser.Options.Location = new Point(-327, -5);
                flyoutPanelBrowser.ShowPopup();
            }
        }

        private void btnPicturesSettings_Click(object sender, EventArgs e)
        {
            //if (flyoutPanel1.IsPopupOpen)
            //    flyoutPanel1.HidePopup();
            //else
            //{
            //    flyoutPanel1.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
            //    flyoutPanel1.Options.Location = new Point(-255, -5);
            //    flyoutPanel1.ShowPopup();
            //}
        }

        private void OpenTagLinkByButton(object sender, ItemClickEventArgs e)
        {
            var hyperLink = e.Item;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }

        private async void repositoryItemPopupContainerEditCategory_Popup(object sender, EventArgs e)
        {
            popupContainerControlCategory.Size = new Size(300, 250);
            treeListCategory.KeyFieldName = "CategoryID";
            //treeListCategory.AfterCheckNode -= TreeListCategory_AfterCheckNode;
            var ebaySite = "eBay US";
            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch1)
            {
                ebaySite = ebaySearch1.EBaySite.SiteName;
            }

            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm1)
            {
                ebaySite = childTerm1.GetParent().EBaySite.SiteName;
            }

            // Handle folder bulk editing - use site from first keyword in folder
            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Search.KeywordFolder folder)
            {
                var firstKeyword = folder.GetAllKeywords().FirstOrDefault();
                if (firstKeyword != null)
                {
                    ebaySite = firstKeyword.EBaySite.SiteName;
                }
            }

            await CategoryLookup
                .InitTreelistLookup(treeListCategory, ebaySite)
                .ContinueWith(t =>
                {
                    var categoriesList = new List<string>();
                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
                    {
                        categoriesList = ebaySearch.Categories4Api.Split(',').ToList();
                    }

                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
                    {
                        categoriesList = childTerm.CategoryIDs.ToList();
                    }

                    // Handle folder bulk editing - show current representative category
                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Search.KeywordFolder folder)
                    {
                        var keywords = folder.GetAllKeywords();
                        if (keywords.Any())
                        {
                            // Get the most common category ID from all keywords
                            var categoryGroups = keywords
                                .Where(k => !string.IsNullOrEmpty(k.Categories4Api))
                                .SelectMany(k => k.Categories4Api.Split(','))
                                .Where(c => !string.IsNullOrEmpty(c.Trim()))
                                .GroupBy(c => c.Trim())
                                .OrderByDescending(g => g.Count())
                                .ToList();

                            if (categoryGroups.Any())
                            {
                                // Show the most common category (up to 3)
                                categoriesList = categoryGroups.Take(3).Select(g => g.Key).ToList();
                            }
                        }
                    }

                    treeListCategory.BeginUpdate();
                    treeListCategory.UncheckAll();

                    for (var i = 0; i < categoriesList.Count; i++)
                    {
                        if (i >= 3)
                            break;

                        var n = categoriesList[i];
                        var nodeToCheck = treeListCategory.FindNodeByKeyID(n);
                        SetParentsCheckState(nodeToCheck, CheckState.Indeterminate);
                        if (nodeToCheck != null)
                            treeListCategory.SetNodeCheckState(nodeToCheck, CheckState.Checked);
                    }

                    treeListCategory.EndUpdate();
                }, TaskScheduler.FromCurrentSynchronizationContext());
        }

        private void SetParentsCheckState(TreeListNode nodeToCheck, CheckState checkState)
        {
            if (nodeToCheck != null && nodeToCheck.ParentNode != null)
            {
                nodeToCheck.ParentNode.CheckState = checkState;
                SetParentsCheckState(nodeToCheck.ParentNode, checkState);
            }
        }

        private void SetChildrenCheckState(TreeListNode nodeToCheck, CheckState checkState)
        {
            if (nodeToCheck.Nodes != null)
                foreach (TreeListNode child in nodeToCheck.Nodes)
                {
                    child.CheckState = checkState;
                    SetChildrenCheckState(child, checkState);
                }
        }

        private void TreeListCategory_AfterCheckNode(object sender, NodeEventArgs e)
        {
            var treeList = (TreeList)sender;

            var checkedNodes = treeList.GetAllCheckedNodes();

            if (checkedNodes.Count > 3)
            {
                e.Node.CheckState = CheckState.Unchecked;
                checkedNodes = treeList.GetAllCheckedNodes();
                for (var i = 0; i < checkedNodes.Count; i++)
                {
                    if (i >= 3)
                        checkedNodes[i].CheckState = CheckState.Unchecked;
                }

                XtraMessageBox.Show(treeList, "Sorry, maximum 3 categories allowed.");

                return;
            }

            if (e.Node.CheckState == CheckState.Unchecked)
            {
                SetParentsCheckState(e.Node, CheckState.Unchecked);
                SetChildrenCheckState(e.Node, CheckState.Unchecked);
            }

            var categoriesIds = new List<string>();
            if (e.Node.CheckState == CheckState.Checked)
            {
                SetParentsCheckState(e.Node, CheckState.Indeterminate);
                SetChildrenCheckState(e.Node, CheckState.Unchecked);
            }

            for (var i = 0; i < checkedNodes.Count; i++)
            {
                if (i >= 3)
                    checkedNodes[i].CheckState = CheckState.Unchecked;
                else
                    categoriesIds.Add(checkedNodes[i].GetValue("CategoryID").ToString());
            }

            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
            {
                ebaySearch.Categories4Api = string.Join(",", categoriesIds);
                if (treeList1.ActiveEditor != null)
                {
                    treeList1.RefreshNode(treeList1.FocusedNode);
                    treeList1.ActiveEditor.EditValue = ebaySearch.Categories4Api;
                }
            }

            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
            {
                childTerm.CategoryIDs = categoriesIds.ToArray();
                if (treeList1.ActiveEditor != null)
                {
                    treeList1.RefreshNode(treeList1.FocusedNode);
                    treeList1.ActiveEditor.EditValue = string.Join(",", childTerm.CategoryIDs);
                }
            }

            // Handle folder bulk editing
            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Search.KeywordFolder folder)
            {
                var categoryString = string.Join(",", categoriesIds);
                if (treeList1.ActiveEditor != null)
                {
                    treeList1.ActiveEditor.EditValue = categoryString;
                    // The VirtualTreeSetCellValue method will be called automatically
                    // when the editor value is set, which will trigger ApplyBulkCategoryIdChange
                }
            }
        }

        private void barCheckPaypalPayment_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            if (barEditItemProfile.EditValue == null || string.IsNullOrWhiteSpace(barEditItemProfile.EditValue.ToString()))
            {
                XtraMessageBox.Show("Browser profile is not selected.");
                return;
            }

            CreditCardService.CreditCardPaymentEnabled = ((BarCheckItem)sender).Checked;
        }

        private void barButtonItemShortcuts_ItemClick(object sender, ItemClickEventArgs e)
        {
            var formShortcuts = new FormShortcuts();
            formShortcuts.ShowDialog();
        }

        private void BarCheckNoConfirmations_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            UserSettings.SkipBuyConfirmation = ((BarCheckItem)sender).Checked;
        }

        private void PopupMenuOpenInBrowser_BeforePopup(object sender, CancelEventArgs e)
        {
            var menuItems = CreateOpenInBrowserMenuItems();
            popupMenuOpenInBrowser.BeginUpdate();
            popupMenuOpenInBrowser.ItemLinks.Clear();
            popupMenuOpenInBrowser.ItemLinks.AddRange(menuItems);
            popupMenuOpenInBrowser.EndUpdate();
        }

        private void BtnSortFilters_Click(object sender, EventArgs e)
        {
            lstchkXfilterList.BeginUpdate();
            XFilterManager.SortFilters();
            lstchkXfilterList.EndUpdate();
        }

        private void barButtonItemTelegram_ItemClick(object sender, ItemClickEventArgs e)
        {
            var formTelegram = new FormTelegram();

            if (_telegramSender == null)
                _telegramSender = new TelegramSender();
            else if (_telegramSender.IsListening())
                _telegramSender.StopListening();
            var tmp = new TelegramSender();

            tmp.TelegramAccount = _telegramSender.TelegramAccount;
            tmp.TelegramBotID = _telegramSender.TelegramBotID;
            tmp.CombinePushes = _telegramSender.CombinePushes;
            tmp.BodyColumns = _telegramSender.BodyColumns;
            tmp.BodyTemplate = _telegramSender.BodyTemplate;

            tmp.Enabled = _telegramSender.Enabled;
            tmp.SetMaxMessagesPerMinute(_telegramSender.MaxMessagesPerMinute);
            tmp.PicturesCountToAttach = _telegramSender.PicturesCountToAttach;
            tmp.TelegramChatID = _telegramSender.TelegramChatID;

            if (!string.IsNullOrEmpty(tmp.TelegramBotID))
            {
                try
                {
                    tmp.CreateTelegramBotClient(tmp.TelegramBotID);
                }
                catch (Exception exception)
                {
                    XtraMessageBox.Show(exception.Message);
                }
            }

            formTelegram.TelegramTmp = tmp;
            formTelegram.ebaySearches = _ebaySearches.ChildrenCore;
            var result = formTelegram.ShowDialog();
            formTelegram.TelegramTmp.StopListening();
            if (result == DialogResult.OK)
            {
                _telegramSender = formTelegram.TelegramTmp;
                FilterActionFactory.Initialize(_telegramSender);
                SaveSettings();
            }

            if (!_telegramSender.IsListening())
            {
                _telegramSender.StartListening();
            }
        }

        private void OpenTagLink(object sender, EventArgs e)
        {
            Process.Start(((LabelControl)sender).Tag.ToString());
        }

        private void repositoryItemPopupContainerEditFilter_QueryResultValue(object sender, QueryResultValueEventArgs e)
        {
            e.Value = filterControlTerm.FilterCriteria;
        }

        internal void ToggleCheckoutPermission()
        {
            if (ConnectionConfig.CheckoutEnabled || ConnectionConfig.SkipBuyConfirmation)
            {
                ribbonPageCheckout.Visible = true;
            }
            else
            {
                ribbonPageCheckout.Visible = false;
            }

            if (ConnectionConfig.CheckoutEnabled)
            {
                barCheckImmediatePayment.Visibility = BarItemVisibility.Always;
                barEditItemProfile.Visibility = BarItemVisibility.Always;
                ribbonPageGroupProfile.Visible = true;
            }
            else
            {
                barCheckImmediatePayment.Checked = false;
                barCheckImmediatePayment.Visibility = BarItemVisibility.Never;
                barEditItemProfile.Visibility = BarItemVisibility.Never;
                ribbonPageGroupProfile.Visible = false;
            }

            if (ConnectionConfig.SkipBuyConfirmation)
            {
                barCheckNoConfirmations.Visibility = BarItemVisibility.Always;
            }
            else
            {
                barCheckNoConfirmations.Checked = false;
                barCheckNoConfirmations.Visibility = BarItemVisibility.Never;
            }

            ribbonPageData.Visible = ConnectionConfig.ShowDataButton;
            ribbonPageGroupExternalData.Visible = ConnectionConfig.ShowDataButton;
            barButtonItemGetExternalData.Visibility = ConnectionConfig.ShowDataButton ? BarItemVisibility.Always : BarItemVisibility.Never;
            barButtonItemGetExternalData.Enabled = ConnectionConfig.ShowDataButton;
        }

        internal void ToggleRestockPermission()
        {
            if (ConnectionConfig.RestockerEnabled)
            {
                barCheckItemRestock.Visibility = BarItemVisibility.Always;
                ribbonPageGroupRestock.Visible = true;
            }
            else
            {
                barCheckItemRestock.Checked = false;
                barCheckItemRestock.Visibility = BarItemVisibility.Never;
                ribbonPageGroupRestock.Visible = false;
            }
        }

        private void barEditItemProfile_EditValueChanged(object sender, EventArgs e)
        {
            CookieManager.Profile = (CookieProfile)((BarEditItem)sender).EditValue;
        }

        private void barStaticLicense_ItemClick(object sender, ItemClickEventArgs e)
        {
            ShowActivationWindow();
        }

        private void barEditItemInitialResultsLimit_EditValueChanged(object sender, EventArgs e)
        {
            if (int.TryParse(((BarEditItem)sender).EditValue.ToString(), out var initialResultsLimit))
            {
                UserSettings.InitialResultsLimit = initialResultsLimit;
            }
        }

        private void barEditItemResultsMaxCount_EditValueChanged(object sender, EventArgs e)
        {
            if (int.TryParse(((BarEditItem)sender).EditValue.ToString(), out var resultsMaxCount))
            {
                UserSettings.MaxResultsCount = resultsMaxCount;
            }
        }

        private void workspaceManager1_WorkspaceSaved(object sender, WorkspaceEventArgs args)
        {
            if (args.Workspace.Name is "Workspace Active" or "Basic Layout (Default)" or "Full Details (Default)" or "DefaultWorkspace")
                return;
            //Analytics.AddEvent("", "WorkspaceSaved", 1);
        }

        private void barButtonItemIgnoreSellers_ItemClick(object sender, ItemClickEventArgs e)
        {
            var authForm = new FormIgnoreSellers();
            var result = authForm.ShowDialog();
            if (result == DialogResult.OK)
            {
                SaveSettings();
            }
        }

        private void CopyFilesFromExtract(string tempExtractPath, string toPath, string fileName)
        {
            var skuScriptPath = Path.Combine(tempExtractPath, fileName);
            if (File.Exists(skuScriptPath))
                File.Copy(skuScriptPath, Path.Combine(toPath, fileName), true);
        }

        private async void barButtonItemRestoreBackup_ItemClick(object sender, ItemClickEventArgs e)
        {
            using var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Backup/config files (*.cfg*, *.zip)|*.cfg*;*.zip";
            openFileDialog.FilterIndex = 1;
            openFileDialog.RestoreDirectory = true;
            openFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
            openFileDialog.Title = "Restore Backup from ZIP file or Config file";
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                if (_watchlistManager == null)
                {
                    // Handle case where manager isn't initialized (show error, return)
                    XtraMessageBox.Show("Watchlist manager not initialized.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 1. Disable future auto-refreshes
                _watchlistManager.SetAutoRefresh(false);

                // 2. Request cancellation of any ongoing refresh
                _watchlistManager.CancelCurrentRefresh();

                // 3. Add a short delay to allow cancellation processing and flag update
                // Adjust delay as needed, slightly more than one potential loop iteration in RefreshWatchlistAsync
                await Task.Delay(300);

                // 4. Final check if refresh is still marked as active (as a safeguard)
                if (_watchlistManager.IsRefreshing)
                {
                    // Consider a slightly different message or shorter wait/retry here if needed,
                    // but for now, we'll assume cancellation should have worked quickly.
                    XtraMessageBox.Show($"Watchlist refresh did not stop promptly after cancellation request. Backup operation cancelled.", "Backup Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    // Optional: Re-enable auto-refresh if needed based on settings
                    // _watchlistManager.SetAutoRefresh(Properties.Settings.Default.WatchlistAutoRefresh);
                    return; // Abort the backup
                }

                // 4. Clear items (only if refresh is confirmed inactive)
                _watchlistManager.ClearItems();

                // 5. Proceed with the original backup logic



                if (_searchService is { Running: true })
                    await StopWorking();

                var backupFilePath = openFileDialog.FileName;
                var fileExtension = Path.GetExtension(backupFilePath).ToLower();

                if (fileExtension == ".zip")
                {
                    // Extract ZIP file
                    var tempExtractPath = Path.Combine(Path.GetTempPath(), "uBuyFirst_Backup");
                    if (Directory.Exists(tempExtractPath))
                        Directory.Delete(tempExtractPath, true);

                    Directory.CreateDirectory(tempExtractPath);
                    ZipFile.ExtractToDirectory(backupFilePath, tempExtractPath);

                    // Load settings from extracted files
                    var configFilePath = Path.Combine(tempExtractPath, "config.cfg");
                    if (File.Exists(configFilePath))
                        LoadSettings(configFilePath);

                    var workSpaceFilePath = Path.Combine(tempExtractPath, "Workspace Active.xml");
                    if (File.Exists(workSpaceFilePath))
                    {
                        File.Copy(workSpaceFilePath, Path.Combine(Folders.Workspace, "Workspace Active.xml"), true);
                        LoadAndApplyWorkSpace();
                    }

                    var layoutFilePath = Path.Combine(tempExtractPath, "Layout Bid Window.xml");
                    if (File.Exists(layoutFilePath))
                        File.Copy(layoutFilePath, Path.Combine(Folders.Layout, "Layout Bid Window.xml"), true);
                    ClearOldResults();

                    // Restore file used by AI and Internal sku manager
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "internalSkuScript.py");

                    // Restory Internal Sku manager
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "SenderFields.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "SenderCategoryFields.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "UniqueFields.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "CsvScriptPath.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "CsvScriptPart.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "RequestCallFields.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "MatchScript.txt");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "DisplayTemplateScript.txt");

                    var csvScriptFiles = FindCsvFilesFromFolder(tempExtractPath);
                    foreach (var csvFile in csvScriptFiles)
                    {
                        CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, csvFile);
                    }

                    // Restore AI stuff
                    CopyFilesFromExtract(tempExtractPath, Path.Combine(Folders.SkuManagerScripts, "_prompts"), "system_message.txt");
                    CopyFilesFromExtract(tempExtractPath, Path.Combine(Folders.SkuManagerScripts, "_templates"), "analysis_result.html");
                    CopyFilesFromExtract(tempExtractPath, Folders.SkuManagerScripts, "tmp.env");
                    // this is tmp file has doesn't have keys from original
                    var envConfigPath = Path.Combine(tempExtractPath, "tmp.env");
                    if (File.Exists(envConfigPath))
                        File.Copy(envConfigPath, Path.Combine(Folders.SkuManagerScripts, ".env"), true);

                    // Add additional restore logic here for other files if needed
                    MessageBox.Show("Backup restored from ZIP file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Restart Python script to apply loaded backup code
                    PythonProcessManager.RestartPythonScriptAfterBackupRestore();

                    // Cleanup the temporary extracted files
                    Directory.Delete(tempExtractPath, true);
                }
                else
                {
                    // Load the settings from the .cfg file
                    LoadSettings(backupFilePath);
                    ClearOldResults();
                    MessageBox.Show("Backup restored from CFG file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Restart Python script to apply loaded backup code
                    PythonProcessManager.RestartPythonScriptAfterBackupRestore();
                }
            }
        }


        private void barButtonItemCreateBackup_ItemClick(object sender, ItemClickEventArgs e)
        {
            // Create a SaveFileDialog to prompt the user where to save the ZIP file
            using var saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "ZIP Files (*.zip)|*.zip";
            saveFileDialog.Title = "Save Backup";
            saveFileDialog.FileName = $"uBuyFirst Backup {DateTime.Now:yyyy-MM-dd}.zip";
            saveFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
            // Show the dialog and check if the user clicked OK
            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                // Get the destination path where the ZIP file will be saved
                var zipFilePath = saveFileDialog.FileName;
                if (File.Exists(zipFilePath))
                {
                    // Delete the existing file
                    File.Delete(zipFilePath);
                }
                BackupToZipFile(zipFilePath);

                MessageBox.Show("Backup created successfully!", "Backup", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private static List<string> FindCsvFilesFromFolder(string folder)
        {
            var retVal = new List<string>();
            if (!Directory.Exists(folder))
            {
                return retVal; // Return empty list
            }
            var files = Directory.GetFiles(folder, "*.csv");
            foreach (var file in files)
            {
                // Add only the filename to the list
                retVal.Add(Path.GetFileName(file));
            }
            return retVal;
        }


        private static List<string> FindCsvFilesForSkuManager()
        {
            var retVal = FindCsvFilesFromFolder(Folders.SkuManagerScripts);
            return retVal;
        }

        private static void AddCsvFilesForSkuManagerToBackup(ZipArchive zipArchive)
        {
            var csvFiles = FindCsvFilesForSkuManager();
            foreach (var csvFile in csvFiles)
            {
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, csvFile);
            }
        }

        private static void MakeTmpEnvFile()
        {
            var folderPath = Folders.SkuManagerScripts;
            // Make tmp.env
            var envFile = Path.Combine(folderPath, ".env");
            var tmpEnvFile = Path.Combine(folderPath, "tmp.env");
            File.Copy(envFile, tmpEnvFile, true);
            // Open tmp.env and remove keys
            var parser = new FileIniDataParser();
            var iniData = parser.ReadFile(tmpEnvFile);
            // key removal part
            iniData["AICONFIG"]["OPENAI_API_KEY"] = "";
            iniData["AICONFIG"]["GEMINI_API_KEY"] = "";
            parser.WriteFile(tmpEnvFile, iniData);
        }

        private static void BackupToZipFile(string zipFilePath)
        {
            // Create the ZIP file and add the necessary files
            using (var zipArchive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
            {
                var configPath = Path.Combine(Folders.Settings, "config.cfg");
                if (File.Exists(configPath))
                {
                    try
                    {
                        // 1. Get sanitized content using the helper method
                        var modifiedXmlContent = ExM.GetSanitizedConfigContent(configPath); // Call helper

                        // 2. Check if sanitization was successful and content is available
                        if (!string.IsNullOrEmpty(modifiedXmlContent))
                        {
                            // 3. Add modified content to zip
                            var configEntry = zipArchive.CreateEntry("config.cfg");
                            using (var entryStream = configEntry.Open())
                            using (var streamWriter = new StreamWriter(entryStream, Encoding.UTF8)) // Ensure UTF8 encoding
                            {
                                streamWriter.Write(modifiedXmlContent);
                            }
                        }
                        else
                        {
                            // Log message already handled within GetSanitizedConfigContent
                            // Inform the user that the config file won't be included due to an error during sanitization
                            MessageBox.Show("Failed to read or sanitize config.cfg.\nIt will not be included in this backup.", "Backup Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception ex) // Catch potential errors during zip creation itself
                    {
                        Log.Error(ex, "Failed to add sanitized config.cfg to backup zip.");
                        MessageBox.Show($"Error adding config.cfg to backup: {ex.Message}\nConfig file will not be included in this backup.", "Backup Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                // Add other files as before
                AddFileToZip(zipArchive, Folders.Workspace, "Workspace Active.xml");
                AddFileToZip(zipArchive, Folders.Layout, "Layout Bid Window.xml");
                // Internal Sku manager adding to zip, internalSkuScript.py is used by both
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "internalSkuScript.py");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "SenderFields.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "SenderCategoryFields.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "UniqueFields.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "CsvScriptPath.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "CsvScriptPart.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "RequestCallFields.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "MatchScript.txt");
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "DisplayTemplateScript.txt");
                AddCsvFilesForSkuManagerToBackup(zipArchive);

                // Add AI stuff to zip
                AddFileToZip(zipArchive, Path.Combine(Folders.SkuManagerScripts, "_prompts"), "system_message.txt");
                AddFileToZip(zipArchive, Path.Combine(Folders.SkuManagerScripts, "_templates"), "analysis_result.html");
                MakeTmpEnvFile();
                AddFileToZip(zipArchive, Folders.SkuManagerScripts, "tmp.env");
            }
        }

        // Helper method to add files to the ZIP archive if they exist
        private static void AddFileToZip(ZipArchive zipArchive, string folderPath, string fileName)
        {
            var sourceFilePath = Path.Combine(folderPath, fileName);
            if (File.Exists(sourceFilePath))
            {
                // Use only the filename for the entry name
                var entryName = Path.GetFileName(fileName); // Changed this line
                zipArchive.CreateEntryFromFile(sourceFilePath, entryName);
            }
        }
        public static void ShowTrayBalloon(string title, string text, int timeout)
        {
            if (Form1.Instance.notifyIcon1 != null && Form1.Instance.IsHandleCreated && !Form1.Instance.IsDisposed)
                Form1.Instance.InvokeIfRequired(() =>
                {
                    Form1.Instance.notifyIcon1.BalloonTipTitle = title;
                    Form1.Instance.notifyIcon1.BalloonTipText = text;
                    Form1.Instance.notifyIcon1.ShowBalloonTip(timeout * 1000);
                });
        }

        private void barCheckItemPriceOpensCheckout_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            UserSettings.ClickOnPriceOpensProductPage = !((BarCheckItem)sender).Checked;
        }

        private void barButtonItemSync_ItemClick(object sender, ItemClickEventArgs e)
        {
            var formSync = new FormSyncSearchTerms();
            _searchTermSyncTimer.Stop();
            formSync.ShowDialog();
            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
            _searchTermSyncTimer.Start();
            SaveSettings();
        }

        private void barButtonItemGetExternalData_ItemClick(object sender, ItemClickEventArgs e)
        {
            var externalDataForm = new FormExternalData(_cefBrowserManager);
            externalDataForm._eBaySearches = _ebaySearches.ChildrenCore;
            externalDataForm.ShowDialog();
        }


        private void pictureSettingsButton_Click(object sender, EventArgs e)
        {
            lock (_chkLargeImagesOnHoverValueChangedLock)
            {
                panelPicturesSettingControl.Visible = !panelPicturesSettingControl.Visible;
            }
        }


        private async void btnRefreshWatchlist_Click(object sender, EventArgs e)
        {
            try
            {
                var currentAccount = await GetCurrentEbayAccountAsync();
                if (currentAccount != null && _watchlistManager != null)
                {
                    await _watchlistManager.TriggerManualRefreshAsync(currentAccount);
                }
            }
            catch (Exception ex)
            {
                LogManager.GetLogger("Logger").Error($"Error refreshing watchlist: {ex.Message}");
                await XtraMessageBox.ShowAsync("Error refreshing watchlist. Please try again.");
            }
        }

        private void timeSpanWatchlistRefreshInterval_EditValueChanged(object sender, EventArgs e)
        {
            // Allow user to set any interval, the manager enforces the 1-min hard limit between actual refreshes.
            // This interval is only used for the auto-refresh timer's frequency.
            if (timeSpanWatchlistRefreshInterval.EditValue is TimeSpan interval && _watchlistManager != null)
            {
                _watchlistManager.SetRefreshInterval(interval);
            }
        }

        private async void chkRefreshAndNotify_EditValueChanged(object sender, EventArgs e)
        {
            if (_watchlistManager == null)
                return;

            var isAutoRefreshEnabled = chkRefreshAndNotifyWatchlist.IsOn;

            // If trying to enable auto-refresh, verify we have a valid account
            if (isAutoRefreshEnabled)
            {
                // Check if we have an account, if not prompt user to add one
                var currentAccount = await GetCurrentEbayAccountAsync();

                // If still no account after prompt, revert toggle and notify user
                if (currentAccount == null)
                {
                    // Turn off the toggle without triggering this event again
                    chkRefreshAndNotifyWatchlist.EditValueChanged -= chkRefreshAndNotify_EditValueChanged;
                    chkRefreshAndNotifyWatchlist.IsOn = false;
                    chkRefreshAndNotifyWatchlist.EditValueChanged += chkRefreshAndNotify_EditValueChanged;

                    XtraMessageBox.Show(
                        "Watchlist auto-refresh requires an eBay account. The feature has been disabled.",
                        "Account Required",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                    return;
                }
            }

            // Now call the business logic - it will double-check account existence
            var success = _watchlistManager.SetAutoRefresh(isAutoRefreshEnabled);

            // If setting auto-refresh failed but toggle is still on, turn it off
            if (!success && isAutoRefreshEnabled)
            {
                chkRefreshAndNotifyWatchlist.EditValueChanged -= chkRefreshAndNotify_EditValueChanged;
                chkRefreshAndNotifyWatchlist.IsOn = false;
                chkRefreshAndNotifyWatchlist.EditValueChanged += chkRefreshAndNotify_EditValueChanged;
                return;
            }

            if (isAutoRefreshEnabled && success)
            {
                // Auto-refresh is ON: Stop cooldown timer, reset button text, disable button
                btnRefreshWatchlist.Text = _originalRefreshButtonText;
                btnRefreshWatchlist.Enabled = false;
            }
            else
            {
                // Auto-refresh is OFF: Check cooldown status
                if (_watchlistManager?.GetHardLimitSecondsRemaining().TotalSeconds > 0)
                {
                    // Cooldown is still active: Keep button disabled and show remaining time
                    btnRefreshWatchlist.Text = $"Wait {Helpers.Get_hhmmss(_watchlistManager.GetHardLimitSecondsRemaining())}s";
                    btnRefreshWatchlist.Enabled = false;
                }
                else
                {
                    // Cooldown is finished: Enable button and set original text
                    btnRefreshWatchlist.Enabled = true;
                    btnRefreshWatchlist.Text = _originalRefreshButtonText;
                }
            }
        }

        private void WatchlistManager_RefreshStarted(object sender, EventArgs e)
        {
            this.InvokeIfRequired(() =>
            {
                btnRefreshWatchlist.Enabled = false;
                btnRefreshWatchlist.Text = "Refreshing...";
            });
        }

        private void WatchlistManager_RefreshCompleted(object sender, EventArgs e)
        {
            this.InvokeIfRequired(() =>
            {
                // Always use the fixed N-second hard limit for the UI cooldown display
                if (!chkRefreshAndNotifyWatchlist.IsOn)
                    btnRefreshWatchlist.Text = $"Wait {Helpers.Get_hhmmss(_watchlistManager.GetHardLimitSecondsRemaining())}s";
                btnRefreshWatchlist.Enabled = false; // Keep disabled during cooldown
            });
        }

        private void WatchlistManager_ItemBecameAvailable(object sender, NotificationEventArgs e)
        {
            // This handler is invoked via SynchronizationContext.Post in WatchlistManager,
            // so it should already be on the UI thread.
            // Add extra checks (e.g., InvokeRequired) if necessary, but likely not needed here.
            try
            {
                CreateNotifications(e.Row, e.ItemData, e.SourceGrid);
            }
            catch (Exception ex)
            {
                // Log error from the event handler
                Log.Error(ex, $"Error in ItemBecameAvailable event handler: {ex.Message}");
                // Consider using a specific LogError method if available
            }
        }


        private void RefreshWatchListRemainingTime()
        {
            this.InvokeIfRequired(() =>
            {

                if (_watchlistManager?.GetHardLimitSecondsRemaining().TotalSeconds > 0)
                {
                    if (chkRefreshAndNotifyWatchlist.IsOn)
                        btnRefreshWatchlist.Text = _originalRefreshButtonText;
                    else
                        btnRefreshWatchlist.Text = @$"Wait {Helpers.Get_hhmmss(_watchlistManager.GetHardLimitSecondsRemaining())}s";
                }
                else
                {
                    btnRefreshWatchlist.Text = _originalRefreshButtonText;
                    btnRefreshWatchlist.Enabled = !chkRefreshAndNotifyWatchlist.IsOn;
                }
                if (_watchlistManager?.GetAutoRefreshSecondsRemaining().TotalSeconds > 0)
                {
                    var timeRemaining = _watchlistManager.GetAutoRefreshSecondsRemaining();

                    if (chkRefreshAndNotifyWatchlist.IsOn)
                        chkRefreshAndNotifyWatchlist.Properties.OnText = @$"Auto Refresh {Helpers.Get_hhmmss(timeRemaining)}s";
                }
            });
        }

        /// <summary>
        /// Updates the restock cooldown status in the status bar
        /// </summary>
        private void UpdateRestockCooldownStatus()
        {
            this.InvokeIfRequired(() =>
            {
                try
                {
                    if (CaptchaCooldownManager.IsInCooldown)
                    {
                        var remaining = CaptchaCooldownManager.RemainingCooldownTime;
                        if (remaining.HasValue)
                        {
                            var minutes = remaining.Value.Minutes;
                            var seconds = remaining.Value.Seconds;
                            var timeText = $"{minutes:00}:{seconds:00}";

                            var cooldownType = CaptchaCooldownManager.GetCooldownType();
                            if (cooldownType == CooldownType.Logout)
                            {
                                barStaticItemRestock.Caption = $"Login required {timeText}";
                                barStaticItemRestock.Appearance.ForeColor = Color.Black;
                                barStaticItemRestock.Appearance.BackColor = Color.Orange;
                            }
                            else // Captcha or default
                            {
                                barStaticItemRestock.Caption = $"Captcha cooldown {timeText}";
                                barStaticItemRestock.Appearance.ForeColor = Color.Red;
                            }

                            barStaticItemRestock.Appearance.Font = new Font(barStaticItemRestock.Appearance.Font, FontStyle.Bold);
                        }
                    }
                    else
                    {
                        barStaticItemRestock.Caption = "";
                        barStaticItemRestock.Appearance.ForeColor = Color.Empty;
                        barStaticItemRestock.Appearance.BackColor = Color.Empty;
                        barStaticItemRestock.Appearance.Font = new Font(barStaticItemRestock.Appearance.Font, FontStyle.Regular);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error updating restock cooldown status: {ex.Message}");
                }
            });
        }

        private void layoutWatchlistControls_LayoutUpgrade(object sender, LayoutUpgradeEventArgs e)
        {
            ((LayoutControl)sender).RestoreDefaultLayout();
        }

        /// <summary>
        /// Gets the current eBay account and optionally prompts the user to add one if none exists
        /// </summary>
        /// <param name="promptIfMissing">Whether to show a prompt to add an account if none exists</param>
        /// <returns>The current eBay account or null if none exists and user declined to add one</returns>
        internal Task<EbayAccount> GetCurrentEbayAccountAsync(bool promptIfMissing = true)
        {
            var currentAccount = EBayAccountsList?.FirstOrDefault();

            if (currentAccount == null && promptIfMissing)
            {
                // Use standard Show method for yes/no dialog since ShowYesNoAsync doesn't exist
                var result = XtraMessageBox.Show(
                    "No eBay account found. Would you like to add one now?",
                    "eBay Account Required",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Show the eBay accounts form
                    using (var formEbayAccounts = new FormEbayAccounts())
                    {
                        formEbayAccounts.ShowDialog();
                        // Refresh the account list after the form closes
                        currentAccount = EBayAccountsList?.FirstOrDefault();
                    }
                }
            }

            return Task.FromResult(currentAccount);
        }

        private async void lnkWatchlistImportFromClipboard_Click(object sender, EventArgs e)
        {
            // Implementation for importing item IDs from clipboard
            var result = ClipboardItemParser.ParseItemIdsFromClipboard();

            if (!result.Success)
            {
                XtraMessageBox.Show(this, result.ErrorMessage ?? "An unknown error occurred while parsing the clipboard.", "Clipboard Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (result.ValidItemIds.Count == 0)
            {
                var invalidMsg = result.InvalidEntries.Count > 0
                    ? $" Found {result.InvalidEntries.Count} invalid entries."
                    : "";
                XtraMessageBox.Show(this, $"No valid 12-digit eBay item IDs found in the clipboard.{invalidMsg}", "Import Watchlist", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // Prepare preview message using the new properties
            var message = $"Found {result.ValidItemIds.Count} valid item ID(s) in the clipboard.\n\n";

            if (result.ValidItemIds.Count > 0)
            {
                message += $"Valid Items Preview:\n{result.ValidItemIdsPreview}\n\n";
            }

            if (result.InvalidEntries.Count > 0)
            {
                message += $"Ignored {result.InvalidEntries.Count} invalid entries.\n";
            }

            // Add warning if exceeding the limit
            if (result.ValidItemIds.Count > 1000)
            {
                message += "\n\n**Warning:** eBay watchlist limit is 1000 items. Only the first 1000 valid items will be added if you continue.";
            }

            // Add warning if importing a large number of items
            if (result.ValidItemIds.Count > 100)
            {
                message += "\n\n**Note:** Importing a large number of items may take a few minutes.";
            }

            message += "\n\nDo you want to add the valid items to your watchlist?";

            // Show confirmation dialog
            var dialogResult = XtraMessageBox.Show(this, message, "Confirm Watchlist Import", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (dialogResult == DialogResult.Yes)
            {
                try
                {
                    if (_watchlistManager != null)
                    {
                        // Limit items if necessary before adding
                        var itemsToAdd = result.ValidItemIds.Count > 1000
                            ? result.ValidItemIds.Take(1000).ToList()
                            : result.ValidItemIds;

                        var allItemsAdded = await _watchlistManager.AddItems(itemsToAdd);

                        // Adjust success message to reflect potentially limited items added
                        var itemsAddedCount = itemsToAdd.Count;
                        var successMessage = $"{itemsAddedCount} item(s) successfully added to the watchlist.";
                        if (result.ValidItemIds.Count > 1000) // Check original count for the message
                        {
                            successMessage += $"\n({result.ValidItemIds.Count - itemsAddedCount} items were omitted due to the 1000 item limit.)";
                        }

                        if (allItemsAdded)
                        {
                            XtraMessageBox.Show(this, successMessage, "Import Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            XtraMessageBox.Show(this, successMessage + "\n\nHowever, some items may not have been added due to an error during the process.", "Import Partially Complete", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error adding items to watchlist from clipboard import.");
                    XtraMessageBox.Show(this, $"An error occurred while adding items to the watchlist: {ex.Message}", "Import Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClearFilterLog_Click(object sender, EventArgs e)
        {
            memoEditFilterLog.Text = "";
        }

        /// <summary>
        /// Handles filter removal logging events from XFilterManager
        /// </summary>
        /// <param name="itemId">The item ID that was removed</param>
        /// <param name="filterAlias">The alias of the filter that triggered the removal</param>
        /// <param name="title">The title of the item that was removed</param>
        /// <param name="timestamp">The timestamp of the removal</param>
        private void OnFilterRemovalLogged(string itemId, string filterAlias, string title, string timestamp)
        {
            try
            {
                // Use Invoke to ensure we're on the UI thread
                if (InvokeRequired)
                {
                    Invoke(new Action<string, string, string, string>(OnFilterRemovalLogged), itemId, filterAlias, title, timestamp);
                    return;
                }

                // Add the log entry to the memo edit control
                memoEditFilterLog.Text += $"\r\n{timestamp}\tRemoved: {itemId} Reason: {filterAlias}\t\t{title} ";

                // Clear the log if it gets too large (same logic as original CheckRemoveFilter)
                if (memoEditFilterLog.Text.Length > 1000000 || memoEditFilterLog.Lines.Length > 500)
                    memoEditFilterLog.Text = "";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OnFilterRemovalLogged: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the visibility of AutoPurchase columns based on ConnectionConfig.RestockerEnabled
        /// </summary>
        private void UpdateAutoPurchaseColumnsVisibility()
        {
            try
            {
                var isEnabled = ConnectionConfig.RestockerEnabled;

                cJobId.Visible = isEnabled;
                cRequiredQuantity.Visible = isEnabled;
                cPurchasedQuantity.Visible = isEnabled;

                // Refresh the treelist to apply visibility changes
                treeList1.RefreshDataSource();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateAutoPurchaseColumnsVisibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Initializes AutoPurchase system visibility based on ConnectionConfig
        /// Should be called after ConnectionConfig is loaded
        /// </summary>
        private void InitializeAutoPurchaseSystemVisibility()
        {
            UpdateAutoPurchaseColumnsVisibility();

            // Restock functionality now uses JSON files instead of database

            // Reinitialize filter action registries to update based on ConnectionConfig
            try
            {
                FilterActionFactory.Reinitialize(_telegramSender);
                FilterActionUIRegistry.Reinitialize();

                // Re-process existing filters to update their ActionHandler after factory re-initialization
                // This ensures filters that couldn't get their ActionHandler during initial loading
                // (because RestockFilterAction wasn't registered yet) get properly initialized now
                ReprocessFilterActionHandlers();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reinitializing filter action registries: {ex.Message}");
            }
        }



        /// <summary>
        /// Re-processes all existing filters to update their ActionHandler after FilterActionFactory changes
        /// This fixes the timing issue where filters are loaded before RestockFilterAction is registered
        /// </summary>
        private void ReprocessFilterActionHandlers()
        {
            try
            {
                var filters = XFilterManager.GetDataSource();
                if (filters != null)
                {
                    Debug.WriteLine($"Starting reprocessing of {filters.Count} filters");

                    foreach (var filter in filters)
                    {
                        Debug.WriteLine($"Processing filter '{filter.Alias}': Action='{filter.Action}', ActionIdentifier='{filter.ActionIdentifier}', ActionHandler={(filter.ActionHandler?.GetType().Name ?? "null")}");

                        // For Restock filters specifically, force re-creation of ActionHandler
                        if (filter.Action == "Restock" && filter.ActionHandler == null)
                        {
                            Debug.WriteLine($"Forcing reprocessing of Restock filter '{filter.Alias}'");

                            // Clear any existing ActionIdentifier to force EnsureModernActionFormat to recreate
                            var originalActionIdentifier = filter.ActionIdentifier;
                            filter.ActionIdentifier = null;

                            // Re-run EnsureModernActionFormat to update ActionHandler based on current factory state
                            filter.EnsureModernActionFormat();

                            Debug.WriteLine($"After reprocessing filter '{filter.Alias}': ActionIdentifier='{filter.ActionIdentifier}', ActionHandler={(filter.ActionHandler?.GetType().Name ?? "null")}");
                        }
                        else
                        {
                            // Re-run EnsureModernActionFormat to update ActionHandler based on current factory state
                            filter.EnsureModernActionFormat();
                        }
                    }
                    Debug.WriteLine($"Completed re-processing {filters.Count} filters for ActionHandler updates");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error re-processing filter action handlers: {ex.Message}");
            }
        }

        private async void barButtonItemRestockReport_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                // Create Reports folder if it doesn't exist
                var reportsFolder = Path.Combine(Folders.Settings, "Reports");
                if (!Directory.Exists(reportsFolder))
                {
                    Directory.CreateDirectory(reportsFolder);
                }

                // Generate timestamped filename
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"RestockReport_{timestamp}.csv";
                var csvFilePath = Path.Combine(reportsFolder, fileName);

                // Create ItemHistoryExporter with default options
                var options = uBuyFirst.RestockReporting.Services.ItemHistoryOptions.CreateDefault();
                var exporter = new uBuyFirst.RestockReporting.Services.ItemHistoryExporter(options);

                // Export data from last 30 days (or all available data)
                var endDate = DateTime.Now;
                var startDate = endDate.AddDays(-30);

                await exporter.ExportToCsvAsync(startDate, endDate, csvFilePath);

                // Show success message
                var message = $"RestockReport CSV file created successfully!\n\nFile: {fileName}\nLocation: {reportsFolder}\n\nThe report contains all restock activity from the last 30 days.";
                XtraMessageBox.Show(message, "RestockReport Created", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Optionally open the folder containing the report
                if (XtraMessageBox.Show("Would you like to open the Reports folder?", "Open Folder", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    Process.Start("explorer.exe", reportsFolder);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error creating RestockReport: {ex.Message}";
                XtraMessageBox.Show(errorMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Log.Error(ex, "Error creating RestockReport CSV");
            }
        }

        private void barCheckItemRestock_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            UserSettings.RestockerEnabled = ((BarCheckItem)sender).Checked;
        }

        private void barEditItemDailySpendLimit_EditValueChanged(object sender, EventArgs e)
        {
            if (sender is BarEditItem barEditItem && decimal.TryParse(barEditItem.EditValue?.ToString(), out var dailyLimit))
            {
                if (dailyLimit >= 0)
                {
                    UserSettings.DailySpendLimit = dailyLimit;
                    UpdateTodaySpentDisplay();
                    //SaveSettings();
                }
            }
        }

        /// <summary>
        /// Updates the barStaticItemTodaySpent display with current daily spend information
        /// </summary>
        public void UpdateTodaySpentDisplay()
        {
            try
            {
                var dailySpendService = new DailySpendService();
                var todaySpent = dailySpendService.GetTodaySpent();
                var dailyLimit = dailySpendService.GetDailyLimit();
                var remainingAmount = Math.Max(0, dailyLimit - todaySpent);

                var displayText = $"Today: ${todaySpent:F2} (${remainingAmount:F2} left)";

                // Thread-safe UI update
                if (InvokeRequired)
                {
                    BeginInvoke(new Action(() => barStaticItemTodaySpent.Caption = displayText));
                }
                else
                {
                    barStaticItemTodaySpent.Caption = displayText;
                }
            }
            catch (Exception ex)
            {
                // Fallback display on error
                var fallbackText = "Daily spend: Error loading";
                if (InvokeRequired)
                {
                    BeginInvoke(new Action(() => barStaticItemTodaySpent.Caption = fallbackText));
                }
                else
                {
                    barStaticItemTodaySpent.Caption = fallbackText;
                }
            }
        }

        private void btnCreateFolder_Click(object sender, EventArgs e)
        {
            CreateNewFolder();
        }

        /// <summary>
        /// Static cleanup method to dispose of the checkout queue semaphore
        /// Call this when shutting down the application
        /// </summary>
        public static void DisposeCheckoutQueueResources()
        {
            try
            {
                _checkoutQueueSemaphore?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing checkout queue semaphore: {ex.Message}");
            }
        }
    }
}
